/**
 * #.# Common SCSS
 *
 * Can include things like variables and mixins
 * that are used across the project.
*/
/**
 * #.# Editor Styles
 *
 * CSS for just Backend enqueued after style.scss
 * which makes it higher in priority.
 */
/**
 * #.# Common SCSS
 *
 * Can include things like variables and mixins
 * that are used across the project.
*/
/**
 * #.# Editor Styles
 *
 * CSS for just Backend enqueued after style.scss
 * which makes it higher in priority.
 */
/**
 * #.# Common SCSS
 *
 * Can include things like variables and mixins
 * that are used across the project.
*/
/**
 * #.# Editor Styles
 *
 * CSS for just Backend enqueued after style.scss
 * which makes it higher in priority.
 */
/**
 * #.# Common SCSS
 *
 * Can include things like variables and mixins
 * that are used across the project.
*/
/**
 * #.# Editor Styles
 *
 * CSS for just Backend enqueued after style.scss
 * which makes it higher in priority.
 */
/**
 * #.# Common SCSS
 *
 * Can include things like variables and mixins
 * that are used across the project.
*/
/**
 * #.# Editor Styles
 *
 * CSS for just Backend enqueued after style.scss
 * which makes it higher in priority.
 */
/**
 * #.# Common SCSS
 *
 * Can include things like variables and mixins
 * that are used across the project.
*/
/**
 * #.# Editor Styles
 *
 * CSS for just Backend enqueued after style.scss
 * which makes it higher in priority.
 */
/**
 * #.# Common SCSS
 *
 * Can include things like variables and mixins
 * that are used across the project.
*/
/**
 * #.# Editor Styles
 *
 * CSS for just Backend enqueued after style.scss
 * which makes it higher in priority.
 */
/**
 * #.# Common SCSS
 *
 * Can include things like variables and mixins
 * that are used across the project.
*/
/**
 * #.# Editor Styles
 *
 * CSS for just Backend enqueued after style.scss
 * which makes it higher in priority.
 */
/**
 * #.# Common SCSS
 *
 * Can include things like variables and mixins
 * that are used across the project.
*/
/**
 * #.# Common SCSS
 *
 * Can include things like variables and mixins
 * that are used across the project.
*/
/**
 * #.# Editor Styles
 *
 * CSS for just Backend enqueued after style.scss
 * which makes it higher in priority.
 */
.embedpress-document-editmode .components-placeholder__instructions:after {
  content: "\ASupported File Type: PDF, DOC, PPT, XLS etc ";
  white-space: pre; }

/* Documents viewer style */
[data-theme-mode="dark"] {
  --viewer-primary-color: #343434;
  --viewer-icons-hover-bgcolor: #453838;
  --viewer-icons-color: #f2f2f6; }

[data-theme-mode="light"] {
  --viewer-primary-color: #f2f2f6;
  --viewer-icons-color: #343434;
  --viewer-icons-hover-bgcolor: #e5e1e9; }

@media (prefers-color-scheme: dark) {
  :root {
    --viewer-primary-color: #343434;
    --viewer-icons-color: #f2f2f6;
    --viewer-icons-hover-bgcolor: #453838; } }

@media (prefers-color-scheme: light) {
  :root {
    --viewer-primary-color: #f2f2f6;
    --viewer-icons-color: #343434;
    --viewer-icons-hover-bgcolor: #e5e1e9; } }

.ep-file-download-option-masked::after,
.ep-external-doc-icons {
  background: var(--viewer-primary-color); }

.ep-external-doc-icons svg path {
  fill: var(--viewer-icons-color); }

.ep-doc-draw-icon svg path {
  fill: var(--viewer-primary-color);
  stroke: var(--viewer-icons-color); }

.ep-external-doc-icons svg:hover svg path {
  fill: var(--viewer-icons-color);
  stroke: var(--viewer-icons-color); }

.ep-external-doc-icons svg:hover {
  background-color: var(--viewer-icons-hover-bgcolor); }

.ep-file-download-option-masked {
  position: relative; }

.embed-download-disabled {
  width: 200px;
  height: 22px;
  background: #444444;
  position: absolute;
  right: 2px;
  bottom: 8px;
  opacity: 0; }

.cui-toolbar-button-right {
  display: none !important; }

.ndfHFb-c4YZDc-Wrql6b {
  display: none; }

.ep-external-doc-icons {
  position: absolute;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: revert;
      flex-direction: revert;
  background: var(--viewer-primary-color);
  border-radius: 6px;
  z-index: 2;
  bottom: -18px;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  padding: 0 10px; }

.ep-external-doc-icons svg {
  width: 30px;
  height: 30px;
  cursor: pointer;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  padding: 5px;
  border-radius: 4px; }

.block-editor-block-list__block .ep-gutenberg-file-doc .ep-external-doc-icons svg {
  width: 22px;
  height: 22px; }

.block-editor-block-list__block .ep-gutenberg-file-doc .ep-doc-download-icon svg,
.block-editor-block-list__block .ep-gutenberg-file-doc .ep-doc-fullscreen-icon svg {
  width: 20px !important; }

.ep-doc-minimize-icon svg,
.ep-doc-fullscreen-icon svg {
  padding: 6px; }

.ep-external-doc-icons svg:hover {
  border-radius: 4px; }

.ep-doc-draw-icon.active svg {
  background: var(--viewer-icons-hover-bgcolor); }

.ep-doc-download-icon,
.ep-doc-print-icon,
.ep-doc-fullscreen-icon,
.ep-doc-popup-icon,
.ep-doc-draw-icon,
.ep-doc-minimize-icon {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-pack: center;
      justify-content: center;
  width: 40px;
  height: 40px;
  /* background: var(--viewer-primary-color); */ }

.elementor .elementor-element.elementor-element .embedpress-document-embed .fullscreen-enabled iframe,
.embedpress-document-embed .fullscreen-enabled iframe {
  width: 100% !important;
  height: 100% !important; }

.ep-file-download-option-masked.ep-file-link.fullscreen-enabled iframe {
  margin-left: 22%; }

.ep-file-download-option-masked::after {
  position: absolute;
  width: 100%;
  height: 30px;
  background: var(--viewer-primary-color);
  z-index: 1;
  bottom: 0;
  content: "";
  left: 0; }

.ep-file-download-option-masked.ep-file-docx::after {
  bottom: 0;
  background: white; }

.ep-file-download-option-masked.ep-file-docx.ep-powered-by-enabled::after {
  bottom: 0px;
  background: white;
  height: 22px; }

.ep-file-download-option-masked.ep-file-docx.ep-powered-by-enabled.fullscreen-enabled::after {
  bottom: 0; }

.ep-file-download-option-masked.ep-file-docx .ep-external-doc-icons {
  right: 12px;
  top: 12px;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
  background: var(--viewer-primary-color);
  border-radius: 6px;
  bottom: auto;
  left: auto;
  -webkit-transform: translate(0%, 0%);
      -ms-transform: translate(0%, 0%);
          transform: translate(0%, 0%); }

canvas.ep-doc-canvas {
  position: absolute;
  z-index: 1;
  left: 0;
  bottom: 0;
  right: 0;
  top: 0;
  clear: both;
  margin: auto;
  display: none; }
/**
 * #.# Common SCSS
 *
 * Can include things like variables and mixins
 * that are used across the project.
*/
/**
 * #.# Editor Styles
 *
 * CSS for just Backend enqueued after style.scss
 * which makes it higher in priority.
 */
.wp-block-embedpress-embedpress iframe {
  max-width: 100%; }

.ose-the-new-york-times iframe {
  min-height: 500px;
  max-height: 100%; }

.block-editor-block-list__layout .wp-block figure,
.block-editor-block-list__layout .wp-block iframe {
  margin: 0; }

@media screen and (max-width: 782px) {
  .block-editor-block-list__layout .wp-block figure,
  .block-editor-block-list__layout .wp-block iframe {
    height: 250px; } }

.wp-block-embedpress-embedpress .components-placeholder.wp-block-embed {
  min-width: 550px; }

.editor-styles-wrapper .block-editor-block-list__layout.is-root-container > .wp-block-embedpress-embedpress {
  max-width: 1200px !important; }

.wp-block-embedpress-embedpress {
  max-width: 100% !important; }

.wp-block-embedpress-embedpress .components-placeholder.wp-block-embed {
  max-width: 100% !important; }

/*Meetup Event styling starts */
.embedpress-event .link {
  color: #0098ab; }

.embedpress-event .visibility--a11yHide {
  border: 0;
  clip: rect(0 0 0 0);
  position: absolute;
  overflow: hidden;
  margin: -1px;
  padding: 0;
  width: 1px;
  height: 1px; }

.embedpress-event .text--small {
  font-size: 14px;
  margin: 0; }

.embedpress-event .flex {
  display: -ms-flexbox;
  display: flex;
  -webkit-box-sizing: border-box;
          box-sizing: border-box; }

.embedpress-event .flex--wrap {
  -ms-flex-wrap: wrap;
      flex-wrap: wrap; }

.embedpress-event .flex--row {
  -ms-flex-align: center;
      align-items: center;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: start;
      justify-content: flex-start;
  -ms-flex-direction: row;
      flex-direction: row;
  -ms-flex-wrap: nowrap;
      flex-wrap: nowrap; }

.embedpress-event .flex-item {
  -ms-flex-preferred-size: 0;
      flex-basis: 0;
  -ms-flex-positive: 1;
      flex-grow: 1;
  width: auto;
  min-width: 0;
  /*padding-left: 16px;*/
  -webkit-box-sizing: border-box;
          box-sizing: border-box; }

.embedpress-event .flex-item--shrink {
  -ms-flex-preferred-size: auto;
      flex-basis: auto;
  -ms-flex-positive: 0;
      flex-grow: 0;
  -ms-flex-negative: 0;
      flex-shrink: 0;
  width: auto; }

.embedpress-event .flex--row > .flex-item:first-child {
  padding-left: 0; }

.embedpress-event .text--bold {
  font-weight: 700; }

.embedpress-event h1,
.embedpress-event h2,
.embedpress-event h3,
.embedpress-event h4,
.embedpress-event h5,
.embedpress-event h6 {
  font-size: inherit; }

.embedpress-event .ep-event--title {
  font-size: 32px;
  font-weight: 700; }

.embedpress-event .ep-event--date {
  color: #757575;
  font-weight: 400;
  font-size: 16px; }

/*Host*/
.embedpress-event .ep-event--host {
  margin-top: 20px;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  margin-bottom: 20px; }

.ep-event--host .avatar-print {
  border-radius: 50%;
  height: 50px;
  width: 50px; }

.embedpress-event img.avatar--person {
  background-image: none !important;
  background-repeat: no-repeat;
  background-position: 50%;
  background-size: cover;
  border-radius: 50%;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  vertical-align: middle; }

.event-info-hosts-text {
  padding-left: 20px;
  font-size: 16px;
  font-weight: 400; }

.embedpress-event .event-description {
  margin-top: 20px; }

.text--sectionTitle {
  font-size: 20px;
  line-height: 28px; }

.ep-event--attendees {
  margin-top: 50px; }

.ep-event--attendees > .flex {
  margin-bottom: 20px; }

.ep-event--attendees .gridList {
  list-style: none;
  margin: 0 -16px 0 0;
  padding: 0; }

.ep-event--attendees .gridList-item {
  width: auto; }

.ep-event--attendees .gridList--autoHeight--has4 > .gridList-item {
  -ms-flex: 0 0 50%;
      flex: 0 0 50%;
  max-width: 50%; }

.ep-event--attendees .groupMember-name {
  line-height: 1.2 !important; }

.ep-event--attendees .avatar--person {
  margin-bottom: 15px;
  display: inline-block;
  border-radius: 50%; }

.ep-event--attendees img.avatar-print {
  border-radius: 50%; }

.ep-event--attendees .groupMember-role {
  font-size: 12px;
  color: #757575;
  padding-top: 2px;
  margin: 0; }

.ep-event--attendees .groupMember {
  min-height: 100%;
  min-width: 128px;
  padding-left: 8px;
  padding-right: 8px; }

.embedpress-event .align--center {
  text-align: center; }

.embedpress-event .card {
  background: #fff;
  background-clip: padding-box;
  background-size: cover;
  border: 1px solid rgba(46, 62, 72, 0.12);
  border-radius: 8px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  display: block;
  min-height: 100%;
  padding: 16px 16px 18px;
  position: relative;
  white-space: normal; }

.embedpress-event .card--hasHoverShadow {
  -webkit-transition: -webkit-box-shadow 0.25s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  transition: -webkit-box-shadow 0.25s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  -o-transition: box-shadow 0.25s cubic-bezier(0.4, 0, 0.2, 1), transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  transition: box-shadow 0.25s cubic-bezier(0.4, 0, 0.2, 1), transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  transition: box-shadow 0.25s cubic-bezier(0.4, 0, 0.2, 1), transform 0.25s cubic-bezier(0.4, 0, 0.2, 1), -webkit-box-shadow 0.25s cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 0.25s cubic-bezier(0.4, 0, 0.2, 1); }

.embedpress-event .ep-event-group-link {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -ms-flex-align: center;
      align-items: center;
  background: #ffffff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid rgba(46, 62, 72, 0.12); }

.embedpress-event .ep-event-group--name {
  padding-left: 20px;
  font-size: 14px;
  line-height: 1.45;
  margin: 0;
  width: 70%;
  word-break: break-word; }

.embedpress-event .ep-event-group--image {
  -o-object-fit: cover;
  object-fit: cover;
  width: 56px;
  height: 56px;
  border-radius: 4px; }

.embedpress-event .ep-event-time-location {
  background: #ffffff;
  border-radius: 8px;
  padding: 16px 20px 0 20px;
  border: 1px solid rgba(46, 62, 72, 0.12); }

.embedpress-event .ep-event-time-location .ep-event-datetime,
.embedpress-event .ep-event-time-location .ep-event-location {
  padding-bottom: 20px; }

.embedpress-event .ep-event-location .wrap--singleLine--truncate,
.embedpress-event .ep-event-time-location .ep-event-datetime {
  font-size: 15px;
  line-height: 1.5;
  color: #2e3e48;
  font-style: normal;
  margin: 0; }

.embedpress-event .ep-event-location address {
  font-style: normal;
  margin: 0; }

.embedpress-event .ep-event-location .venueDisplay-venue-address {
  font-style: normal;
  color: #757575;
  margin: 0; }

.embedpress-event .ep-event-location p {
  line-height: 20px; }

.ep-event--attendees .gridList-item {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  display: inline-block;
  font-size: 1rem;
  margin: 0;
  vertical-align: top;
  width: 50%; }

.gridList-itemInner {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  height: 100%;
  padding: 0 16px 16px 0; }

@media only screen and (min-width: 530px) {
  .ep-event--attendees .gridList--autoHeight--has4 > .gridList-item {
    -ms-flex: 0 0 33.333333%;
        flex: 0 0 33.333333%;
    max-width: 33.333333%; } }

@media only screen and (min-width: 640px) {
  .embedpress-event .card {
    padding: 18px 18px 20px; }
  .ep-event--attendees .gridList--autoHeight--has4 > .gridList-item {
    -ms-flex: 0 0 25%;
        flex: 0 0 25%;
    max-width: 25%; } }

/*--------------------------------------------------------------
# Alignments
--------------------------------------------------------------*/
.editor-block-list__block[data-align=right] > .editor-block-list__block-edit {
  float: right; }

.wp-block[data-align=left], .wp-block[data-align=right] {
  height: auto !important; }

@media only screen and (min-width: 482px) {
  .editor-styles-wrapper .wp-block[data-align=right] > *,
  .editor-styles-wrapper .wp-block[data-align=left] > * {
    max-width: 100% !important; } }

.editor-styles-wrapper .wp-block[data-align=center] {
  text-align: center; }

.editor-styles-wrapper .wp-block::after {
  content: '';
  display: block;
  clear: both;
  min-height: 1px; }

.alignleft {
  display: inline;
  float: left;
  margin-right: 1.5em; }

.alignright {
  display: inline;
  float: right;
  margin-left: 1.5em; }

.aligncenter {
  clear: both;
  display: block;
  margin-left: auto;
  margin-right: auto; }

.cbutton-preview-wrapper {
  text-align: center;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-pack: center;
      justify-content: center;
  -ms-flex-direction: column;
      flex-direction: column;
  background: #fff8f8;
  height: 200px;
  width: 300px;
  margin: auto;
  margin-top: -350px; }

h4.cbutton-preview-text {
  padding-top: 0;
  margin-top: 0; }
/**
 * #.# Common SCSS
 *
 * Can include things like variables and mixins
 * that are used across the project.
*/
/**
 * #.# Editor Styles
 *
 * CSS for just Backend enqueued after style.scss
 * which makes it higher in priority.
 */
.embedpress-document-editmode .components-placeholder__instructions:after {
  content: "\ASupported File Type: PDF, DOC, PPT, XLS etc ";
  white-space: pre; }

.wp-block-embedpress-embedpress-pdf + * {
  clear: both; }

.embedpress-document-editmode .components-placeholder__instructions:after {
  display: none !important; }
/**
 * #.# Common SCSS
 *
 * Can include things like variables and mixins
 * that are used across the project.
*/
/**
 * #.# Editor Styles
 *
 * CSS for just Backend enqueued after style.scss
 * which makes it higher in priority.
 */
.wp-block-embedpress-embedpress iframe,
.wp-block-embedpress-calendar iframe {
  max-width: 100%; }

.block-editor-block-list__layout .wp-block figure,
.block-editor-block-list__layout .wp-block iframe {
  margin: 0; }

@media screen and (max-width: 782px) {
  .block-editor-block-list__layout .wp-block figure,
  .block-editor-block-list__layout .wp-block iframe {
    height: 250px; } }

/*--------------------------------------------------------------
# Alignments
--------------------------------------------------------------*/
.editor-block-list__block[data-align=right] > .editor-block-list__block-edit {
  float: right; }

.wp-block[data-align=left], .wp-block[data-align=right] {
  height: auto !important; }

@media only screen and (min-width: 482px) {
  .editor-styles-wrapper .wp-block[data-align=right] > *,
  .editor-styles-wrapper .wp-block[data-align=left] > * {
    max-width: 100% !important; } }

.editor-styles-wrapper .wp-block[data-align=center] {
  text-align: center; }

.editor-styles-wrapper .wp-block::after {
  content: '';
  display: block;
  clear: both;
  min-height: 1px; }

.alignleft {
  display: inline;
  float: left;
  margin-right: 1.5em; }

.alignright {
  display: inline;
  float: right;
  margin-left: 1.5em; }

.aligncenter {
  clear: both;
  display: block;
  margin-left: auto;
  margin-right: auto; }
