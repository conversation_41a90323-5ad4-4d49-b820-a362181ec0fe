/**
 * #.# Common SCSS
 *
 * Can include things like variables and mixins
 * that are used across the project.
*/
/**
 * #.# Styles
 *
 * CSS for both Frontend+Backend.
 */
/**
 * #.# Common SCSS
 *
 * Can include things like variables and mixins
 * that are used across the project.
*/
/**
 * #.# Styles
 *
 * CSS for both Frontend+Backend.
 */
/**
 * #.# Common SCSS
 *
 * Can include things like variables and mixins
 * that are used across the project.
*/
/**
 * #.# Styles
 *
 * CSS for both Frontend+Backend.
 */
/**
 * #.# Common SCSS
 *
 * Can include things like variables and mixins
 * that are used across the project.
*/
/**
 * #.# Styles
 *
 * CSS for both Frontend+Backend.
 */
/**
 * #.# Common SCSS
 *
 * Can include things like variables and mixins
 * that are used across the project.
*/
/**
 * #.# Styles
 *
 * CSS for both Frontend+Backend.
 */
/**
 * #.# Common SCSS
 *
 * Can include things like variables and mixins
 * that are used across the project.
*/
/**
 * #.# Styles
 *
 * CSS for both Frontend+Backend.
 */
/**
 * #.# Common SCSS
 *
 * Can include things like variables and mixins
 * that are used across the project.
*/
/**
 * #.# Styles
 *
 * CSS for both Frontend+Backend.
 */
/**
 * #.# Common SCSS
 *
 * Can include things like variables and mixins
 * that are used across the project.
*/
/**
 * #.# Styles
 *
 * CSS for both Frontend+Backend.
 */
/**
 * #.# Common SCSS
 *
 * Can include things like variables and mixins
 * that are used across the project.
*/
/**
 * #.# Common SCSS
 *
 * Can include things like variables and mixins
 * that are used across the project.
*/
/**
 * #.# Styles
 *
 * CSS for both Frontend+Backend.
 */
.pdfobject-container {
  height: 600px;
  width: 600px;
  margin: 0 auto; }

.embedpress-el-powered {
  text-align: center;
  margin-top: 0 !important;
  font-size: 16px !important;
  font-weight: 700; }

.embedpress-embed-document iframe,
[data-type="embedpress/document"] iframe {
  margin: 0 auto;
  display: block; }

.embedpress-embed-document-pdf {
  max-width: 100%; }

.embedpress-document-editmode .components-form-file-upload {
  display: none !important; }

.components-toggle-control .components-base-control__field .components-form-toggle {
  margin-right: 6px; }

.opensea-control.pro-control span.isPro {
  right: 6px;
  bottom: 5px;
  -webkit-transform: scale(0.9);
      -ms-transform: scale(0.9);
          transform: scale(0.9); }

.pro-control.opensea-control input {
  border-color: #cfc2c2;
  color: #cfc2c2;
  border-radius: 5px; }

.pro-control .components-base-control {
  pointer-events: none; }

.pro-control .sponsored-manager-controllers {
  opacity: 0.5; }

.sponsored-manager-controllers.opacity {
  opacity: 0.3; }

.pro-control.ep-custom-logo-button button {
  opacity: 0.3; }

.ads-help {
  margin-top: 30px; }

button.components-flex.components-color-palette__custom-color {
  height: 20px; }

.components-color-palette__custom-color-value {
  display: none !important; }

.components-circular-option-picker .components-circular-option-picker__swatches {
  -ms-flex-pack: distribute;
      justify-content: space-around; }

.wp-block-embedpress-document.embedpress-document-embed {
  max-width: 100% !important; }

.components-edit-button {
  border-right: 1px solid #1e1e1e;
  border-radius: 0; }

@media only screen and (max-width: 767px) {
  .wp-block-embedpress-document.embedpress-document-embed {
    max-height: 400px !important; }
  .ep-file-download-option-masked {
    height: 100%; }
  iframe.embedpress-embed-document-pdf {
    max-height: 480px; } }
/**
 * #.# Common SCSS
 *
 * Can include things like variables and mixins
 * that are used across the project.
*/
/**
 * #.# Styles
 *
 * CSS for both Frontend+Backend.
 */
.embedpress-gutenberg-wrapper {
  margin: 30px auto; }
  .embedpress-gutenberg-wrapper .ose-embedpress-responsive:not(.ose-youtube) iframe {
    max-width: 100% !important;
    max-height: 100% !important; }

.embedpress-gutenberg-wrapper.alignright {
  max-width: 100%; }

.ose-the-new-york-times iframe {
  min-height: 500px;
  max-height: 100%; }

body.page .flexia-wrapper > .content-area {
  padding: 0 !important; }

.flexia-wrapper.flexia-container > .content-area {
  margin: 0 !important; }
  .flexia-wrapper.flexia-container > .content-area .embedpress-gutenberg-wrapper {
    margin: 0 auto; }

@media only screen and (min-width: 482px) {
  .entry-content > .embedpress-gutenberg-wrapper.alignright,
  .entry-content > .embedpress-gutenberg-wrapper.alignleft,
  .embedpress-gutenberg-wrapper.alignright,
  .embedpress-gutenberg-wrapper.alignleft {
    max-width: 100%; } }

.embedpress-gutenberg-wrapper .alignleft,
.embedpress-gutenberg-wrapper .alignright,
.embedpress-gutenberg-wrapper .aligncenter {
  float: none;
  display: block; }

.embedpress-gutenberg-wrapper .alignright {
  text-align: right;
  max-width: 100% !important; }

.embedpress-gutenberg-wrapper .alignleft {
  text-align: left;
  max-width: 100% !important; }

.embedpress-gutenberg-wrapper .aligncenter {
  clear: both;
  display: block;
  float: none;
  margin-right: auto;
  margin-left: auto;
  text-align: center; }

.wp-block-embedpress-embedpress.aligncenter {
  text-align: center; }

.embedpress-gutenberg-wrapper .ose-embedpress-responsive {
  display: inline-block; }

.embedpress-gutenberg-wrapper {
  max-width: 100% !important; }

.ep-first-video {
  position: relative;
  height: 0;
  padding-bottom: 56.25%;
  border-radius: 15px;
  overflow: hidden; }

.ep-first-video iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%; }

.embedpress-gutenberg-controls .components-base-control__label {
  font-weight: 500; }

.embedpress-gutenberg-controls .components-input-control__label {
  font-weight: 500; }

.embedpress-gutenberg-controls .components-base-control__field,
.embedpress-gutenberg-controls .components-input-control__label {
  font-weight: 500; }

.sponsored-upload {
  margin-bottom: 20px; }

label.components-truncate.components-text.components-input-control__label.em5sgkm4.ecfd-bd--d-eecc-1iznhho.em57xhy0 {
  font-weight: 600 !important; }

.text-center {
  text-align: center !important; }

.wp-block-embed.is-loading {
  -ms-flex-align: center;
      align-items: center; }

.css-vwt1e3 {
  margin: 0px 11px 0px !important; }

.ep-youtube__content__pagination .ep-prev,
.ep-youtube__content__pagination .ep-next {
  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none; }

.ep__components-placeholder,
.wp-block-embedpress-embedpress {
  clear: both !important; }

.wp-block-embed.is-loading p {
  margin: 0 !important; }

.wp-block-embed.is-loading svg {
  margin-top: 0; }

/**
* NFT card frontend style
*/
.ose-opensea {
  height: 100% !important;
  width: 100% !important;
  min-width: 900px; }

.alignleft .ose-opensea,
.alignright .ose-opensea,
.aligncenter .ose-opensea {
  min-width: 900px;
  max-width: calc(900px - 40px) !important; }

.aligncenter .ose-opensea {
  margin: 0 auto;
  display: block !important; }

.wp-block[data-align="right"] {
  float: right; }

.wp-block[data-align="left"] {
  float: left; }

.ep_nft_content_wrap.ep_nft__wrapper {
  display: grid; }

.ep_nft_content_wrap.ep_nft__wrapper,
.ep_nft_content_wrap.ep_nft_list {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  grid-column-gap: 15px;
  grid-row-gap: 15px; }

.ep_nft_content_wrap .ep_nft_item {
  padding-top: 15px;
  padding-right: 15px;
  padding-left: 15px;
  padding-bottom: 15px;
  background-color: #ffffff;
  border-radius: 10px;
  -webkit-transition: background 0.5s, border 0.5s, border-radius 0.5s, -webkit-box-shadow 0.5s;
  transition: background 0.5s, border 0.5s, border-radius 0.5s, -webkit-box-shadow 0.5s;
  -o-transition: background 0.5s, border 0.5s, border-radius 0.5s, box-shadow 0.5s;
  transition: background 0.5s, border 0.5s, border-radius 0.5s, box-shadow 0.5s;
  transition: background 0.5s, border 0.5s, border-radius 0.5s, box-shadow 0.5s, -webkit-box-shadow 0.5s;
  -webkit-box-shadow: 0 4px 15px rgba(0, 0, 0, 0.09);
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.09);
  overflow: hidden;
  padding: 15px;
  position: relative;
  -webkit-transition: 0.3s ease-in-out;
  -o-transition: 0.3s ease-in-out;
  transition: 0.3s ease-in-out; }

.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-grid .ep_nft_item {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column; }

.ep_nft_content_wrap.ep_nft_list .ep_nft_item {
  -ms-flex-pack: start;
      justify-content: flex-start;
  -ms-flex-align: start;
      align-items: flex-start; }

.ep_nft_content_wrap.ep_nft__wrapper.preset-3 .ep_nft_item .ep_nft_content {
  background-color: #edecf6e6; }

.ep_nft_content_wrap .ep_nft_thumbnail {
  margin-top: 0px;
  margin-right: 0px;
  margin-left: 0px;
  margin-bottom: 15px;
  border-radius: 5px; }

.ep_nft_content_wrap .ep_nft_thumbnail img {
  height: 340px;
  border-radius: 10px;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover; }

.ep_nft_content .ep_nft_title {
  color: #333333;
  font-size: 16px;
  margin-top: 0px;
  margin-right: 0px;
  margin-left: 0px;
  margin-bottom: 15px;
  font-weight: 600; }

.ep_nft_content {
  text-align: left; }

.ep_nft_content .ep_nft_price {
  color: #333333;
  font-size: 14px;
  margin-top: 0px;
  margin-right: 0px;
  margin-left: 0px;
  margin-bottom: 0px;
  display: -ms-flexbox;
  display: flex;
  font-weight: 600; }

.ep_nft_content .ep_nft_price:first-child {
  margin-bottom: 10px; }

span.eb_nft_currency {
  max-width: 28px;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-pack: center;
      justify-content: center; }

span.eb_nft_currency svg {
  width: 100%;
  height: auto; }

.ep_nft_content .ep_nft_price_wrapper {
  min-height: 20px; }

.ep_nft_content .ep_nft_creator {
  color: #333333;
  font-size: 14px;
  margin-top: 0px;
  margin-right: 0px;
  margin-left: 0px;
  margin-bottom: 20px;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  gap: 5px; }

.ep_nft_content .ep_nft_creator a {
  color: #3080e2;
  font-size: 14px;
  text-decoration: none;
  word-break: break-all; }

.ep_nft_content .ep_nft_creator img {
  height: 30px;
  width: 30px;
  border-radius: 50%; }

.ep_nft_content .ep_nft_button button {
  margin-top: 0px;
  margin-right: 0px;
  margin-left: 0px;
  margin-bottom: 0px; }

.ep_nft_content .ep_nft_button button a {
  background-color: #3080e2;
  color: #ffffff;
  font-size: 14px;
  padding-top: 15px;
  padding-right: 20px;
  padding-left: 20px;
  padding-bottom: 15px;
  -webkit-transition: border 0.5s, border-radius 0.5s, -webkit-box-shadow 0.5s;
  transition: border 0.5s, border-radius 0.5s, -webkit-box-shadow 0.5s;
  -o-transition: border 0.5s, border-radius 0.5s, box-shadow 0.5s;
  transition: border 0.5s, border-radius 0.5s, box-shadow 0.5s;
  transition: border 0.5s, border-radius 0.5s, box-shadow 0.5s, -webkit-box-shadow 0.5s; }

.ep_nft_content .ep_nft_button button:hover a {
  background-color: #2e8eee;
  color: #ffffff; }

.ep-nft-gallery-wrapper .ep_nft_content_wrap.ep_nft__wrapper.ep-preset-1 .ep_nft_item:hover .ep_nft_button {
  opacity: 1;
  -webkit-transform: translate(0);
      -ms-transform: translate(0);
          transform: translate(0);
  visibility: visible; }

.ep-nft-gallery-wrapper.ep-nft-gallery-r1a5mbx .ep_nft_button a.ep-details-btn:hover {
  background-color: #2e8eee;
  color: #ffffff; }

.ep-nft-gallery-wrapper .ep_nft_content_wrap.ep_nft__wrapper.ep-preset-1 .ep_nft_item:hover .ep_nft_button {
  opacity: 1;
  -webkit-transform: translate(0);
      -ms-transform: translate(0);
          transform: translate(0);
  visibility: visible; }

.ep-nft-gallery-wrapper .ep_nft_content_wrap.ep_nft__wrapper.ep-preset-1 .ep_nft_item .ep_nft_button {
  bottom: 0;
  left: 0;
  opacity: 0;
  position: absolute;
  -webkit-transform: translateY(30px);
      -ms-transform: translateY(30px);
          transform: translateY(30px);
  visibility: hidden;
  width: 100%;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s; }

.ep-nft-gallery-wrapper.ep-nft-gallery-r1a5mbx .ep_nft_button {
  margin-top: auto; }

.ep-nft-gallery-wrapper.ep-nft-gallery-r1a5mbx .ep_nft_button a {
  background-color: #3080e2;
  color: #ffffff;
  font-size: 14px;
  padding: 10px 20px;
  -webkit-transition: border 0.5s, border-radius 0.5s, -webkit-box-shadow 0.5s;
  transition: border 0.5s, border-radius 0.5s, -webkit-box-shadow 0.5s;
  -o-transition: border 0.5s, border-radius 0.5s, box-shadow 0.5s;
  transition: border 0.5s, border-radius 0.5s, box-shadow 0.5s;
  transition: border 0.5s, border-radius 0.5s, box-shadow 0.5s, -webkit-box-shadow 0.5s;
  display: block;
  text-align: center;
  font-weight: 500;
  text-decoration: none; }

/* mimmikcssStart */
/* NFT List item CSS */
.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-list .ep_nft_item {
  display: -ms-flexbox;
  display: flex;
  gap: 15px;
  -ms-flex-align: center;
      align-items: center;
  border-radius: 10px; }

.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-list .ep_nft_item .ep_nft_thumbnail {
  width: 55%; }

.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-list .ep_nft_item .ep_nft_content {
  width: calc(45% - 15px); }

.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-list .ep_nft_item .ep_nft_content .ep_nft_price.ep_nft_last_sale {
  margin-bottom: 15px; }

.ep_nft_content_wrap.ep_nft__wrapper.nft_items .ep_nft_item .ep_nft_thumbnail svg {
  border-radius: 10px; }

.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-list .ep_nft_item .ep_nft_thumbnail img {
  height: 260px; }

.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-list .ep_nft_item .ep_nft_button a {
  border-radius: 10px; }

.ep_nft_content_wrap.ep_nft__wrapper.nft_items .ep_nft_item .ep_nft_button a {
  border-radius: 10px; }

.ep-nft-gallery-wrapper .ep_nft_content_wrap.ep_nft__wrapper.ep-preset-1 .ep_nft_item .ep_nft_button a {
  border-radius: 0; }

.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-grid.ep-preset-2 .ep_nft_content .ep_nft_price:last-child {
  margin-bottom: 15px; }

.ose-opensea {
  min-width: 100% !important;
  max-width: calc(100% - 40px) !important; }

.ep-loadmore-wrapper {
  margin-top: 30px;
  text-align: center; }

.ep-loadmore-wrapper button {
  border-style: solid;
  border-top-width: 0;
  border-right-width: 0;
  border-left-width: 0;
  border-bottom-width: 0;
  color: #ffffff;
  border-color: #0170b9;
  background-color: #0170b9;
  border-radius: 2px;
  padding-top: 15px;
  padding-right: 30px;
  padding-bottom: 15px;
  padding-left: 30px;
  font-family: inherit;
  font-weight: inherit;
  line-height: 1;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-pack: center;
      justify-content: center;
  margin: auto;
  cursor: pointer; }

.ep-loadmore-wrapper button svg {
  margin-left: 5px; }

/* mimmikcssEnd */
@media all and (max-width: 1024px) {
  /* tabcssStart */
  .ep_nft_content_wrap.ep_nft__wrapper,
  .ep_nft_content_wrap.ep_nft_list {
    grid-template-columns: repeat(3, 1fr) !important; }
  /* tabcssEnd */ }

@media all and (max-width: 991px) {
  /* tabcssStart */
  .ose-opensea {
    min-width: auto !important;
    max-width: calc(100% - 40px) !important; }
  .ep_nft_content_wrap.ep_nft__wrapper,
  .ep_nft_content_wrap.ep_nft_list {
    grid-template-columns: repeat(2, 1fr) !important; }
  /* tabcssEnd */ }

@media all and (max-width: 767px) {
  /* mobcssStart */
  .ep_nft_content_wrap.ep_nft__wrapper,
  .ep_nft_content_wrap.ep_nft_list {
    grid-template-columns: repeat(1, 1fr) !important; }
  /* mobcssEnd */ }

/* NFT Single item CSS */
.ep-nft-single-item-wraper.ep_nft_content_wrap .ep_nft_item {
  -webkit-box-shadow: none;
          box-shadow: none; }

.ep-nft-single-item-wraper.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-list .ep_nft_item .ep_nft_content {
  width: calc(55% - 15px); }

.ep-nft-single-item-wraper.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-list .ep_nft_item .ep_nft_thumbnail {
  width: 45%;
  height: 100%; }

.ep-nft-single-item-wraper.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-list .ep_nft_item .ep_nft_thumbnail img {
  height: 100%; }

.ep-nft-single-item-wraper span.eb_nft_label {
  color: #333; }

.ep-nft-single-item-wraper .ep_nft_content .ep_nft_price {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
  margin-right: 45px; }

.ep-nft-single-item-wraper span.eb_nft_price {
  font-size: 30px; }

.ep-nft-single-item-wraper .ep_nft_content .ep_nft_creator img {
  height: 15px;
  width: 15px;
  border-radius: 50%; }

.ep-nft-single-item-wraper .ep-usd-price {
  bottom: 0;
  font-size: 12px; }

.ep-nft-single-item-wraper span.eb_nft_label {
  font-size: 15px; }

.ep-nft-single-item-wraper .ep_nft_content .ep_nft_title {
  margin-bottom: 10px;
  font-size: 24px; }

.ep-nft-single-item-wraper .CollectionLink--name svg {
  width: 20px;
  height: 20px;
  margin-left: 5px; }

.ep-nft-single-item-wraper a.CollectionLink--link {
  font-size: 15px;
  font-weight: 500;
  text-decoration: none;
  margin-bottom: 15px;
  display: block;
  color: #3080e2; }

.ep-nft-single-item-wraper sub.verified-icon {
  bottom: -5px;
  left: 4px; }

/* toggle */
.ep-nft-single-item-wraper .ep-accordion {
  border: 1px solid #ddd;
  border-radius: 10px;
  margin-top: 15px;
  display: block; }

.ep-nft-single-item-wraper .ep-toggle {
  display: none; }

.ep-nft-single-item-wraper .ep-option {
  position: relative; }

.ep-nft-single-item-wraper .ep-content {
  padding: 1em;
  border-top: 1px solid #ddd; }

.ep-nft-single-item-wraper .ep-content {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  transition: all 0.2s; }

.ep-nft-single-item-wraper .ep-title {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transform: translateZ(0);
          transform: translateZ(0);
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
  transition: all 0.2s;
  padding: 1em;
  display: -ms-flexbox;
  display: flex;
  color: #333;
  font-weight: bold;
  cursor: pointer;
  border-radius: 10px 10px 0 0;
  -ms-flex-align: center;
      align-items: center; }

.ep-nft-single-item-wraper label.ep-title svg {
  width: 20px;
  height: 20px;
  margin-right: 6px; }

.ep-nft-single-item-wraper .ep-asset-detail-item {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: justify;
      justify-content: space-between;
  font-weight: 500;
  line-height: 1.8em; }

.ep-nft-single-item-wraper .ep-asset-detail-item span {
  word-break: break-word;
  max-width: 75%; }

.ep-nft-single-item-wraper .ep-content {
  display: block; }

.ep-nft-single-item-wraper .ep-content p {
  margin: 0;
  padding: 0.5em 1em 1em;
  font-size: 0.9em;
  line-height: 1.5; }

.ep-nft-single-item-wraper .ep-toggle:checked + .ep-title + .ep-content {
  display: none;
  background: transparent; }

.ep-nft-single-item-wraper .ep_nft_content .ep_nft_price_wrapper {
  display: -ms-flexbox;
  display: flex; }

.ep-nft-single-item-wraper .ep-toggle + .ep-title:after {
  content: "";
  display: inline-block;
  position: absolute;
  width: 12px;
  height: 12px;
  background: transparent;
  text-indent: -9999px;
  border-top: 2px solid #bfbfbf;
  border-left: 2px solid #bfbfbf;
  -webkit-transition: all 250ms ease-in-out;
  -o-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  text-decoration: none;
  color: transparent;
  right: 15px;
  top: 50%;
  -webkit-transform: rotate(45deg) translate(-20%, -5%);
      -ms-transform: rotate(45deg) translate(-20%, -5%);
          transform: rotate(45deg) translate(-20%, -5%); }

.ep-nft-single-item-wraper .ep-toggle:checked + .ep-title:before {
  content: "";
  display: inline-block;
  position: absolute;
  width: 12px;
  height: 12px;
  background: transparent;
  text-indent: -9999px;
  border-top: 2px solid #bfbfbf;
  border-left: 2px solid #bfbfbf;
  -webkit-transition: all 250ms ease-in-out;
  -o-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  text-decoration: none;
  color: transparent;
  right: 15px;
  top: 50%;
  -webkit-transform: rotate(225deg) translate(80%, 20%);
      -ms-transform: rotate(225deg) translate(80%, 20%);
          transform: rotate(225deg) translate(80%, 20%); }

.ep-nft-single-item-wraper .ep-toggle:checked + .ep-title:after {
  display: none; }

.ep-nft-single-item-wraper.ep_nft_content_wrap.ep_nft__wrapper.nft_items .ep_nft_item .ep_nft_button span.ep-nft-rank {
  pointer-events: none; }

.ep-nft-single-item-wraper.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-nft-single-item-wraper.ep-grid
.ep_nft_content {
  margin-bottom: 20px; }

@media screen and (max-width: 1024px) {
  .ep-nft-single-item-wraper.ep_nft_content_wrap.ep_nft__wrapper,
  .ep-nft-single-item-wraper.ep_nft_content_wrap.ep_nft_list {
    grid-template-columns: repeat(1, 1fr) !important; } }

@media screen and (max-width: 991px) {
  .ep-nft-single-item-wraper.ep_nft_content_wrap.ep_nft__wrapper,
  .ep-nft-single-item-wraper.ep_nft_content_wrap.ep_nft_list {
    grid-template-columns: repeat(1, 1fr) !important; }
  .ep-nft-single-item-wraper.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-list .ep_nft_item {
    -ms-flex-align: unset;
        align-items: unset;
    -ms-flex-direction: column;
        flex-direction: column; }
  .ep-nft-single-item-wraper.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-list .ep_nft_item .ep_nft_thumbnail {
    width: 100%; }
  .ep-nft-single-item-wraper.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-list .ep_nft_item .ep_nft_content {
    width: 100%; }
  .ep-embed-content-wraper > div {
    max-width: 100%; } }

@media screen and (max-width: 537px) {
  .ep-nft-single-item-wraper sub.ep-usd-price {
    margin-bottom: 15px;
    display: block; } }

.components-panel__body .ep-control-header {
  border-top: 1px solid #fbf0f0;
  padding-top: 16px;
  width: calc(100% + 32px);
  margin-left: -16px;
  padding-left: 16px; }

button.components-color-palette__custom-color-button {
  height: 24px; }

.components-color-palette__custom-color-text-wrapper {
  display: none !important; }

.components-panel__body.is-opened.ep-opensea-options h2.components-panel__body-title,
.components-panel__body.is-opened.ep-colors-typography h2.components-panel__body-title {
  margin-bottom: 16px !important; }

.components-panel__body.is-opened.ep-colors-typography .ep-opensea-sub-controls h2.components-panel__body-title {
  margin-bottom: 0px !important; }

.embedpress-gutenberg-controls .components-panel__body.is-opened h2.components-panel__body-title {
  margin-bottom: 16px !important; }

.ep-controls-margin {
  margin-bottom: 24px; }

.ep__instafeed-options .instagram-profile-image-uploader {
  margin-bottom: 0; }

.block-editor-block-inspector .components-base-control:last-child {
  margin-bottom: 24px !important; }

.ep-video-controlers .ep-control-field .components-base-control__field {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: justify;
      justify-content: space-between;
  -ms-flex-align: center;
      align-items: center; }

.ep-video-controlers .ep-control-field .components-base-control__field input {
  width: 70px; }

.ep-video-controlers .ep-control-field .components-base-control__field .components-base-control__label {
  margin-bottom: 0px; }

.ep-video-controlers .components-base-control .components-base-control__label {
  margin-right: 0px; }

button.ep-remove__image {
  position: absolute;
  z-index: 1;
  color: white;
  font-weight: bold;
  border: none;
  padding: 0px;
  top: 10px;
  margin: 22px;
  left: 10px;
  background: #f11c1d;
  width: 32px;
  height: 32px;
  text-align: center;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  -ms-flex-pack: center;
      justify-content: center;
  border-radius: 50px;
  padding: 0;
  margin: 0;
  cursor: pointer; }

.ep__custom-logo {
  text-align: center;
  margin-bottom: 15px; }

.ep__custom-logo img {
  width: 100%;
  height: 200px;
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 5px; }

.ep-logo-upload-button {
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 20px 15px; }

.instagram-profile-image-uploader {
  margin-bottom: 20px; }

.ep-yt-related-videos .components-base-control {
  margin-bottom: 10px !important; }

.content-share-controls .ep-custom-logo-button {
  margin-bottom: 30px !important; }

.pro-control.ep-custom-logo-button {
  margin-bottom: 20px !important; }

.ep-custom-logo-button .isPro {
  top: 50%;
  -webkit-transform: translate(0%, -50%);
      -ms-transform: translate(0%, -50%);
          transform: translate(0%, -50%); }

.ep-custom-logo-position {
  margin-top: 20px; }

.ep-yt-related-videos p,
.ep-video-controlers p {
  margin-top: calc(8px);
  font-size: 12px;
  font-style: normal;
  color: #757575;
  margin-bottom: revert; }

p.is-ep-description {
  margin-top: -20px !important;
  color: #757575; }

span.ep-wistia-message {
  position: absolute;
  left: 50%;
  background: white;
  border-radius: 15px;
  padding: 8px 15px;
  top: 32%;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: calc(100% - 200px);
  text-align: center;
  max-width: 320px; }

@media only screen and (max-width: 520px) {
  .wistia_embed,
  .ose-wistia {
    max-height: 280px; } }

.lock-content-pass-input {
  position: relative; }

.lock-content-pass-input span {
  position: absolute;
  right: 8px;
  z-index: 1;
  cursor: pointer;
  bottom: 3.5px;
  display: none; }

.lock-content-pass-input span.active {
  display: block; }

span.copy-password.active {
  top: 0;
  width: 20px;
  background: #5b4e96;
  height: 20px;
  border-radius: 15px;
  padding: 3px;
  right: 0; }

span.copy-tooltip {
  top: 0;
  display: block;
  right: 10px;
  font-weight: 600;
  background: #5b4e96;
  height: -webkit-max-content;
  height: -moz-max-content;
  height: max-content;
  color: #fff;
  border-radius: 15px;
  font-size: 12px;
  visibility: none;
  opacity: 0;
  -webkit-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  position: absolute;
  padding: 1px 8px;
  font-size: 12px;
  opacity: 0; }

.copy-tooltip.show {
  opacity: 1;
  visibility: visible;
  right: 25px; }

.gutenberg-block-wraper,
.gutenberg-wraper {
  position: relative; }

.gutenberg-block-wraper {
  display: inline-block; }

.gutenberg-block-wraper.ep-content-share-enabled .ep-embed-content-wraper {
  display: -ms-inline-flexbox !important;
  display: inline-flex !important;
  -ms-flex-pack: center;
      justify-content: center;
  -ms-flex-align: center;
      align-items: center; }

.gutenberg-block-wraper.ep-content-share-enabled .ep-embed-content-wraper {
  -ms-flex-align: center;
      align-items: center; }

.gutenberg-block-wraper.ep-content-share-enabled .position-left-wraper.ep-embed-content-wraper {
  -ms-flex-align: self-start;
      align-items: self-start; }

.gutenberg-block-wraper.ep-content-share-enabled .position-right-wraper.ep-embed-content-wraper {
  -ms-flex-align: self-start;
      align-items: self-start; }

.gutenberg-block-wraper.source-opensea {
  display: block; }

.wp-block-embedpress-embedpress > div {
  position: relative; }

/* social share css */
.gutenberg-wraper {
  display: -ms-flexbox;
  display: flex; }

.position-right-wraper.gutenberg-pdf-wraper,
.position-left-wraper.gutenberg-pdf-wraper,
.position-top-wraper.gutenberg-pdf-wraper,
.position-bottom-wraper.gutenberg-pdf-wraper {
  width: 100%; }

.ep-share-position-right .gutenberg-wraper {
  -ms-flex-direction: row;
      flex-direction: row; }

.ep-share-position-left .gutenberg-wraper {
  -ms-flex-direction: row-reverse;
      flex-direction: row-reverse; }

.ep-share-position-bottom .gutenberg-wraper {
  -ms-flex-direction: column;
      flex-direction: column; }

.ep-share-position-top .gutenberg-wraper {
  -ms-flex-direction: column-reverse;
      flex-direction: column-reverse; }

.ep-social-share {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center; }

.ep-social-share.share-position-right {
  right: -40px;
  top: 0;
  -ms-flex-direction: column;
      flex-direction: column; }

.ep-social-share.share-position-left {
  left: 0px;
  top: 0;
  -ms-flex-direction: column;
      flex-direction: column; }

.embedpress-gutenberg-wrapper .ep-social-share.share-position-left,
.ep-elementor-content .ep-social-share.share-position-left {
  left: -40px; }

.ep-social-share.share-position-top {
  -ms-flex-pack: center;
      justify-content: center; }

.ep-social-share.share-position-bottom {
  -ms-flex-pack: center;
      justify-content: center; }

.ep-social-icon {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: center;
      justify-content: center;
  -ms-flex-align: center;
      align-items: center;
  width: 40px;
  height: 40px;
  margin: 0;
  color: #fff;
  text-decoration: none;
  font-size: 20px; }

.ep-social-icon:hover {
  opacity: 0.8; }

.ep-social-share svg {
  width: 20px;
  height: auto; }

a.ep-social-icon.pinterest svg {
  height: 25px; }

.ep-social-share .facebook {
  background-color: #3b5998; }

.ep-social-share .facebook svg {
  width: 40px; }

.ep-social-share .twitter {
  background-color: #000000; }

a.ep-social-icon.twitter:focus-visible {
  -webkit-box-shadow: none;
          box-shadow: none;
  border: none;
  outline: none; }

.ep-social-share .pinterest {
  background-color: #bd081c; }

.ep-social-share .linkedin {
  background-color: #0077b5; }

.ep-social-share .reddit {
  background-color: #ff4500; }

.ep-social-icon i {
  margin-right: 0; }

.gutenberg-block-wraper .ep-social-share.share-position-left {
  left: -40px;
  top: 0;
  -ms-flex-direction: column;
      flex-direction: column; }

.block-editor-block-list__block .ep-social-share {
  pointer-events: none; }

.custom-share-thumbnail-label {
  font-size: 11px;
  font-weight: 500;
  line-height: 1.4;
  text-transform: uppercase;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  display: block;
  padding-top: 0px;
  padding-bottom: 0px;
  max-width: 100%;
  z-index: 1;
  overflow: hidden;
  -o-text-overflow: ellipsis;
     text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 8px; }

.lock-content-controllers .components-form-toggle.is-checked .components-form-toggle__track,
.content-share-toggle .components-form-toggle.is-checked .components-form-toggle__track {
  background-color: var(--wp-components-color-accent, var(--wp-admin-theme-color, #6354a5)); }

.ep-pannel-icon {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  gap: 10px; }

.ep-pannel-icon svg {
  width: 18px;
  height: auto; }

.ep-documentation,
.ep-tips-and-tricks {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
      align-items: center;
  gap: 10px;
  margin-bottom: 5px; }

.ep-documentation svg,
.ep-tips-and-tricks svg {
  width: 25px;
  height: auto; }

.ep-documentation a,
.ep-tips-and-tricks a {
  font-weight: 500;
  font-size: 13px;
  color: #5b4e96; }

.ep-calendly-data-link {
  display: -ms-flexbox;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-align: center;
  align-items: center;
  gap: 5px;
  margin-bottom: 5px;
  padding: 0 10px;
  margin-top: -10px !important;
  margin-bottom: 15px; }

.ep-calendly-data-link svg {
  width: 22px;
  height: auto; }

.ep-calendly-data-link a {
  font-weight: 500;
  font-size: 14px;
  color: #5b4e96; }

.tips__alert__wrap {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: #0000009c;
  z-index: 99999;
  display: none; }

.tips__alert__wrap .tips__alert__card {
  width: calc(100% - 30px);
  max-width: 500px;
  margin: 7% auto 0;
  background: #fff;
  border-radius: 20px;
  padding: 30px;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
  -ms-flex-align: center;
      align-items: center;
  text-align: center; }

.tips__alert__wrap .tips__alert__card img {
  height: 150px; }

.tips__alert__wrap .tips__alert__card h2 {
  font-size: 17.5px;
  font-weight: 500;
  color: #131f4d;
  margin-bottom: 15px; }

.tips__alert__wrap .tips__alert__card p {
  font-size: 14px;
  font-weight: 400;
  color: #7c8db5;
  margin-bottom: 15px; }

.tips__alert__wrap .tips__alert__card a {
  text-decoration: underline;
  font-weight: 700;
  color: #131f4d; }

.tips__alert__wrap .tips__alert__card .button {
  -ms-flex-item-align: end;
      align-self: flex-end;
  margin-top: 20px;
  padding: 6px 30px;
  border-radius: 10px;
  text-decoration: none; }

p.ep-live-video-info {
  display: -ms-flexbox;
  display: flex;
  gap: 8px;
  font-size: 12px;
  font-weight: bold;
  background: #f6d88c;
  padding: 8px 8px;
  border-radius: 10px; }

p.ep-live-video-info svg {
  width: 20px; }

.embedpress-gutenberg-controls .components-text-control__input::-webkit-input-placeholder,
.embedpress-gutenberg-controls .components-textarea-control__input::-webkit-input-placeholder {
  color: #757575; }

.embedpress-gutenberg-controls .components-text-control__input::-moz-placeholder,
.embedpress-gutenberg-controls .components-textarea-control__input::-moz-placeholder {
  color: #757575; }

.embedpress-gutenberg-controls .components-text-control__input::-ms-input-placeholder,
.embedpress-gutenberg-controls .components-textarea-control__input::-ms-input-placeholder {
  color: #757575; }

.embedpress-gutenberg-controls .components-text-control__input::placeholder,
.embedpress-gutenberg-controls .components-textarea-control__input::placeholder {
  color: #757575; }

button.skip-ad-button {
  position: absolute;
  bottom: 15px;
  right: 10px;
  border: none;
  background: #d41556b5 !important;
  padding: 6px 8px;
  color: white !important;
  z-index: 122222222;
  font-size: 15px;
  font-weight: bold;
  border-radius: 4px;
  padding: 0; }

.components-panel__row.elementor-panel-alert.elementor-panel-warning-info {
  margin-top: -15px;
  display: inline-block;
  font-size: 12px;
  font-weight: bold;
  background: #f6d88c;
  padding: 8px 8px;
  border-radius: 10px; }

.components-panel__row.elementor-panel-alert.elementor-panel-warning-info.margin-bottom-10 {
  margin-bottom: 10px; }

.control-description {
  margin-top: -10px !important;
  margin-bottom: 20px;
  font-size: 12px;
  font-style: normal;
  color: #757575; }

.ep-select-control {
  margin-bottom: 16px; }
  .ep-select-control .ep-select-control-label {
    font-size: 11px;
    font-weight: 500;
    line-height: 1.4;
    text-transform: uppercase;
    -webkit-box-sizing: border-box;
            box-sizing: border-box;
    display: block;
    padding-top: 0px;
    padding-bottom: 0px;
    max-width: 100%;
    z-index: 1;
    overflow: hidden;
    -o-text-overflow: ellipsis;
       text-overflow: ellipsis;
    white-space: nowrap; }
  .ep-select-control .dropdown {
    position: relative; }
  .ep-select-control .dropdown-header {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-pack: justify;
        justify-content: space-between;
    -ms-flex-align: center;
        align-items: center;
    border: 1px solid #ccc;
    padding: 8px;
    cursor: pointer;
    background-color: #fff;
    border-radius: 4px;
    font-weight: 600; }
  .ep-select-control span.arrow-dropdown svg {
    width: 18px;
    height: 18px; }
  .ep-select-control .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    border: 1px solid #ccc;
    background-color: #fff;
    border-radius: 4px;
    z-index: 10;
    max-height: 250px;
    overflow-y: auto;
    -webkit-box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 8px; }
  .ep-select-control .search-input {
    width: 100%;
    padding: 5px 8px;
    margin-bottom: 8px;
    border: 1px solid #ccc;
    border-radius: 4px; }
  .ep-select-control .dropdown-item {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
        align-items: center;
    padding: 8px;
    cursor: pointer;
    border-radius: 4px; }
    .ep-select-control .dropdown-item label {
      cursor: pointer; }
  .ep-select-control .dropdown-item:hover {
    background-color: #f0f0f0; }
  .ep-select-control .dropdown-item.selected {
    background-color: #e6f7ff; }
  .ep-select-control .dropdown-item input {
    margin-right: 8px; }
  .ep-select-control .no-options {
    padding: 8px;
    text-align: center;
    color: #888; }

.plugin-rating {
  font-family: system-ui;
  padding: 15px;
  border-top: 1px solid #e0e0e0; }
  .plugin-rating h4 {
    margin-bottom: 8px;
    font-size: 15px;
    font-weight: 500;
    color: #1d2939;
    margin-top: 0; }
  .plugin-rating .stars {
    display: -ms-flexbox;
    display: flex;
    gap: 5px;
    margin-bottom: 20px; }
    .plugin-rating .stars .star {
      color: #b1b8c2;
      cursor: pointer;
      width: 20px;
      height: 20px; }
  .plugin-rating .tankyou-msg-container,
  .plugin-rating .feedback-submit-container {
    margin-top: 10px;
    border-radius: 8px;
    text-align: left;
    position: relative;
    margin-bottom: 25px; }
    .plugin-rating .tankyou-msg-container textarea.form-control,
    .plugin-rating .feedback-submit-container textarea.form-control {
      width: 100%;
      background: #fff;
      outline: 1px solid #ebe1f2;
      margin-bottom: 5px;
      border: none; }
      .plugin-rating .tankyou-msg-container textarea.form-control::-webkit-input-placeholder,
      .plugin-rating .feedback-submit-container textarea.form-control::-webkit-input-placeholder {
        font-weight: 400;
        font-size: 12px;
        line-height: 1.8;
        color: #5f6c7f; }
      .plugin-rating .tankyou-msg-container textarea.form-control::-moz-placeholder,
      .plugin-rating .feedback-submit-container textarea.form-control::-moz-placeholder {
        font-weight: 400;
        font-size: 12px;
        line-height: 1.8;
        color: #5f6c7f; }
      .plugin-rating .tankyou-msg-container textarea.form-control::-ms-input-placeholder,
      .plugin-rating .feedback-submit-container textarea.form-control::-ms-input-placeholder {
        font-weight: 400;
        font-size: 12px;
        line-height: 1.8;
        color: #5f6c7f; }
      .plugin-rating .tankyou-msg-container textarea.form-control::placeholder,
      .plugin-rating .feedback-submit-container textarea.form-control::placeholder {
        font-weight: 400;
        font-size: 12px;
        line-height: 1.8;
        color: #5f6c7f; }
    .plugin-rating .tankyou-msg-container textarea:focus,
    .plugin-rating .feedback-submit-container textarea:focus {
      outline-color: #5b4e96;
      -webkit-box-shadow: none !important;
              box-shadow: none !important;
      outline: 1px solid #5b4e96; }
    .plugin-rating .tankyou-msg-container .submit-button,
    .plugin-rating .tankyou-msg-container .rating-button,
    .plugin-rating .feedback-submit-container .submit-button,
    .plugin-rating .feedback-submit-container .rating-button {
      border-radius: 4px;
      border-width: 1px;
      padding: 8px;
      width: 100%;
      border: 1px solid #5b4e96;
      color: #5b4e96;
      background: #fff;
      cursor: pointer;
      display: -ms-flexbox;
      display: flex;
      -ms-flex-align: center;
          align-items: center;
      -ms-flex-pack: center;
          justify-content: center; }
      .plugin-rating .tankyou-msg-container .submit-button svg,
      .plugin-rating .tankyou-msg-container .rating-button svg,
      .plugin-rating .feedback-submit-container .submit-button svg,
      .plugin-rating .feedback-submit-container .rating-button svg {
        height: 18px;
        width: 18px; }
    .plugin-rating .tankyou-msg-container .help-message,
    .plugin-rating .feedback-submit-container .help-message {
      font-weight: 500;
      font-size: 14px;
      line-height: 12px;
      letter-spacing: 0%;
      margin-bottom: 15px;
      margin-top: 0; }
    .plugin-rating .tankyou-msg-container p.form-description,
    .plugin-rating .feedback-submit-container p.form-description {
      font-size: 12px; }
    .plugin-rating .tankyou-msg-container span.close-icon,
    .plugin-rating .feedback-submit-container span.close-icon {
      position: absolute;
      top: 8px;
      right: 8px; }
      .plugin-rating .tankyou-msg-container span.close-icon svg,
      .plugin-rating .feedback-submit-container span.close-icon svg {
        height: 12px;
        width: 12px;
        cursor: pointer; }
    .plugin-rating .tankyou-msg-container span.undo-review,
    .plugin-rating .feedback-submit-container span.undo-review {
      color: #5b4e96;
      font-weight: 400;
      text-decoration: none;
      cursor: pointer; }
  .plugin-rating p.thank-you-message {
    font-weight: 400;
    color: #5f6c7f;
    margin-bottom: 15px;
    font-size: 14px; }
  .plugin-rating .chat-button {
    background-color: #5b4e96;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: center;
    justify-content: center;
    gap: 5px;
    font-weight: 400;
    width: 100%;
    text-decoration: none; }
    .plugin-rating .chat-button svg {
      width: 18px;
      height: 18px; }
    .plugin-rating .chat-button:hover {
      background-color: #4b3293; }
  .plugin-rating .upgrade-box {
    padding: 15px;
    margin-top: 20px;
    border-radius: 8px;
    text-align: left;
    background: -webkit-linear-gradient(268.68deg, #fffbf8 1.12%, #ffffff 98.95%);
    background: -o-linear-gradient(268.68deg, #fffbf8 1.12%, #ffffff 98.95%);
    background: linear-gradient(181.32deg, #fffbf8 1.12%, #ffffff 98.95%);
    border: 0.6px solid #f4efec; }
    .plugin-rating .upgrade-box h5 {
      font-size: 14px;
      margin-top: 0;
      margin-bottom: 10px;
      color: #1d2939;
      font-weight: 600; }
    .plugin-rating .upgrade-box p {
      font-size: 12px;
      color: #232c39;
      margin-bottom: 12px;
      font-weight: 400; }
    .plugin-rating .upgrade-box .upgrade-link {
      color: #ec6e00;
      font-weight: 400;
      text-decoration: none; }
      .plugin-rating .upgrade-box .upgrade-link:hover {
        text-decoration: underline; }

.plugin-rating.turn_off_ratting_help {
  padding: 0;
  margin: 0; }
  .plugin-rating.turn_off_ratting_help .upgrade-box {
    margin: 0;
    border-radius: 0; }

.photos-gallery-grid .ose-google-photos,
.photos-gallery-masonary .ose-google-photos,
.photos-gallery-justify .ose-google-photos {
  height: 100% !important;
  max-height: 100% !important; }
/**
 * #.# Common SCSS
 *
 * Can include things like variables and mixins
 * that are used across the project.
*/
/**
 * #.# Styles
 *
 * CSS for both Frontend+Backend.
 */
.pdfobject-container {
  height: 600px;
  width: 600px;
  margin: 0 auto; }

.embedpress-el-powered {
  text-align: center;
  margin-top: 10px !important;
  font-size: 16px !important;
  font-weight: 700; }

.embedpress-embed-document iframe,
[data-type="embedpress/document"] iframe {
  margin: 0 auto;
  display: block; }

.embedpress-embed-document {
  max-width: 100%; }

.embedpress-document-editmode .components-form-file-upload {
  display: none !important; }

.wp-block-embedpress-embedpress-pdf + * {
  clear: both; }

.pro-control {
  position: relative;
  margin-bottom: 20px; }

span.isPro {
  position: absolute;
  z-index: 1;
  background: #5b4e96;
  border-radius: 20px;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 40px;
  height: 18px;
  font-size: 10px;
  right: 5px;
  color: #fff;
  font-weight: 400;
  text-transform: uppercase;
  bottom: 0;
  top: 0; }

.pro-control:after {
  content: "";
  width: 100%;
  height: 100%;
  background: transparent;
  position: absolute;
  z-index: 12;
  top: 0;
  bottom: 0;
  left: 0; }

.theme-astra span.isPro {
  right: 10px;
  bottom: 5px; }

.pro-control:hover::after {
  cursor: pointer; }

.pro__alert__wrap {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1;
  display: none; }

.pro__alert__wrap .pro__alert__card {
  width: calc(100% - 30px);
  max-width: 500px;
  margin: 7% auto 0;
  background: #fff;
  border-radius: 20px;
  padding: 30px;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
      flex-direction: column;
  -ms-flex-align: center;
      align-items: center;
  text-align: center; }

.pro__alert__wrap .pro__alert__card h2 {
  font-size: 32px;
  font-weight: 450;
  color: #131f4d;
  margin-bottom: 15px; }

.pro__alert__wrap .pro__alert__card p {
  font-size: 14px;
  font-weight: 400;
  color: #7c8db5;
  margin-top: 10px; }

.pro__alert__wrap .pro__alert__card .button {
  -ms-flex-item-align: end;
  align-self: flex-end;
  margin-top: 20px;
  padding: 6px 30px;
  border-radius: 10px; }

.pro__alert__wrap .pro__alert__card p a {
  text-decoration: underline;
  font-weight: 700;
  color: #131f4d; }

.template__wrapper .button:hover {
  background: #5b4e96 !important;
  color: #ffffff !important;
  border-color: #5b4e96 !important; }

.pro__alert__wrap .pro__alert__card img {
  height: 100px;
  margin-bottom: 20px; }

.pro-control .components-toggle-control__label {
  color: #bbacac; }

.pro-control .components-form-toggle .components-form-toggle__track {
  border: 1px solid #b89d9d; }

.pro-control .components-form-toggle .components-form-toggle__thumb {
  background-color: #b89d9d;
  border: 5px solid #b89d9d; }

.ep-gutenberg-controls-wrapper .components-base-control {
  margin-top: 15px;
  margin-bottom: 30px; }

.ep-gutenberg-controls-wrapper .remove-last-child-margin .components-base-control:last-child {
  margin-bottom: 30px; }

.embedpress-documents-control .components-base-control {
  margin-top: 0 !important; }

.pro-control-active {
  margin-bottom: 24px; }

.disabled-content-protection .pro-control-active,
.disabled-content-protection .pro-control {
  margin-bottom: 0; }

.disabled-content-protection .pro-control-active .components-toggle-control,
.disabled-content-protection .pro-control .components-toggle-control {
  margin-bottom: 10px; }

.lock-content-pass-input {
  position: relative; }

.ep-alignleft {
  text-align: left; }

.ep-alignright {
  text-align: right; }

.ep-aligncenter {
  text-align: center; }

.embedpress-document-embed.embedpress-pdf {
  width: 100%; }

.embedpress-document-embed {
  position: relative; }

.embedpress-inner-iframe {
  text-align: center;
  position: relative; }

.ep-fixed-width .embedpress-inner-iframe {
  display: inline-block; }

.embedpress-inner-iframe.ep-alignright {
  text-align: right; }

.embedpress-inner-iframe.ep-aligncenter {
  text-align: center; }

.embedpress-inner-iframe.ep-alignleft {
  text-align: left; }

.ep__components-placeholder,
.wp-block-embedpress-embedpress {
  clear: both !important; }

.ep-pdf-width-contol {
  position: relative; }

.ep-unit-choice-option {
  margin: 0 !important;
  position: absolute;
  top: 5px;
  right: 0; }
  .ep-unit-choice-option .components-base-control {
    margin-bottom: 0px !important; }

.ep-unit-choice-option .components-base-control__field .components-flex,
.ep-unit-choice-option .components-flex {
  -ms-flex-direction: row !important;
      flex-direction: row !important;
  -ms-flex-pack: end;
      justify-content: end;
  gap: 10px;
  padding: 4px 5px;
  border-radius: 4px; }

.ep-unit-choice-option .components-radio-control__option {
  position: relative; }

.ep-unit-choice-option .components-radio-control__option label {
  position: absolute;
  top: 50%;
  /* position the top  edge of the element at the middle of the parent */
  left: 50%;
  /* position the left edge of the element at the middle of the parent */
  -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  font-size: 10px;
  left: -18px; }

.ep-unit-choice-option .components-radio-control__input {
  margin: 0;
  border: none;
  width: 20px !important;
  height: 20px !important;
  max-width: 20px !important;
  max-height: 20px !important; }

.ep-unit-choice-option .components-radio-control__input[type="radio"]:checked:before {
  background-color: var(--wp-admin-theme-color);
  border: 4px solid var(--wp-admin-theme-color); }

.ep-unit-choice-option .components-radio-control__input[type="radio"]:checked + label {
  color: white; }

.ep-pdf-width-contol .ep-control-header {
  font-size: 11px; }

.emebedpress-unit-percent iframe {
  width: 100% !important; }

.components-panel__body .components-base-control__field {
  padding: 0; }

@media only screen and (max-width: 850px) {
  .embedpress-inner-iframe.emebedpress-unit-percent {
    width: 100% !important; } }

.presentationModeEnabledIosDevice {
  position: fixed;
  left: 0;
  top: 0;
  border: 0;
  height: 100% !important;
  width: 100% !important;
  z-index: 999999;
  min-width: 100% !important;
  min-height: 100% !important; }

.components-edit-button {
  border-right: 1px solid #1e1e1e;
  border-radius: 0; }
/**
 * #.# Common SCSS
 *
 * Can include things like variables and mixins
 * that are used across the project.
*/
/**
 * #.# Styles
 *
 * CSS for both Frontend+Backend.
 */
.embedpress-calendar-gutenberg {
  margin: 30px auto; }
  .embedpress-calendar-gutenberg iframe {
    max-width: 100%; }

.embedpress-calendar-gutenberg.alignright {
  max-width: 100%; }

body.page .flexia-wrapper > .content-area {
  padding: 0 !important; }

.flexia-wrapper.flexia-container > .content-area {
  margin: 0 !important; }
  .flexia-wrapper.flexia-container > .content-area .embedpress-calendar-gutenberg {
    margin: 0 auto; }

@media only screen and (min-width: 482px) {
  .entry-content > .embedpress-calendar-gutenberg.alignright,
  .entry-content > .embedpress-calendar-gutenberg.alignleft,
  .embedpress-calendar-gutenberg.alignright,
  .embedpress-calendar-gutenberg.alignleft {
    max-width: 100%; } }
