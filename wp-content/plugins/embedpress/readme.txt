=== EmbedPress – PDF Embedder PDF 3D FlipBook, Social Feeds, Google Docs, Vimeo, Wistia, YouTube Videos, Maps Embed & Upload PDF Documents ===
Contributors: EmbedPress, asif2bd, re_enter_rupok, wpdevteam, sea<PERSON><PERSON><PERSON>, alimuzzamanalim
Author: WPDeveloper
Author URI: https://wpdeveloper.com
Tags: embed, embed YouTube, embed PDF, Google Docs, Social Feeds
Requires at least: 4.6
Tested up to: 6.8
Requires PHP: 5.6
Stable tag: 4.2.8
License: GPLv3 or later
License URI: https://opensource.org/licenses/GPL-3.0

EmbedPress lets you embed videos, pages, social feeds, embed PDF 3D flipbooks & other content on Word<PERSON>ress without coding & enhance storytelling.

== Description ==

= EMBEDPRESS – EMBED FROM UNLIMITED SOURCES – EMBED PDF WITH 3D FLIPBOOK VIEWER, EMBED YOUTUBE, SOCIAL FEEDS, GOOGLE DOCS, MAPS, VIMEO, WISTIA, SPOTIFY, ETC WITHOUT CODING & DISPLAY IN WEBSITES CREATED WITH  ELEMENTOR, GUTENBERG BLOCK EDITOR, OR OTHER PAGE BUILDERS =

[EmbedPress](https://embedpress.com/) enhances the interactive storytelling on your WordPress website by offering one-click embeds of videos, social feeds, maps, PDFs, 3D flipbooks, posts, pages, documents, and much more from unlimited multimedia content sources.

Fuel up your website's engagement and make it aesthetically pleasing by embedding content directly in Classic Editor, Gutenberg Block Editor, Elementor, or by using EmbedPress shortcodes on other WordPress page builders with ease.

https://youtu.be/fvYKLkEnJbI

### 🔥 ALL-IN-ONE WORDPRESS EMBEDDING PLUGIN SUPPORTS UNLIMITED SOURCES

With EmbedPress, all you need is a URL to embed attention-grabbing content from over unlimited websites. Find the URL for your preferred video, image, audio file, post, or map, and simply cut and paste the link into a WordPress post. EmbedPress automatically understands the URL and instantly embeds fully responsive content on your websites. EmbedPress also makes it simple to embed PDF with 3D flipbook viewer in only one click.

### 🔗 COMPATABLE WITH GUTENBERG EMBED & OTHER WORDPRESS PAGE BUILDERS

EmbedPress makes embedding engaging content on Classic Editor, Gutenberg Block Editor & Elementor completely hassle-free for you with advanced customizations.

Or, you can also effortlessly use advanced [EmbedPress shortcodes](https://embedpress.com/docs/how-to-use-embedpress-shortcodes-page-builders/) to embed content into any of your preferred WordPress Page Builders, like Beaver Builder & Divi, in less than a minute.

### 📄 [EMBED PDF](https://embedpress.com/embed-pdf-document/), DOC, PPT, OR ALMOST ANY FILE TYPE, RIGHT FROM WORDPRESS

Upload PDF, PPT (Powerpoint Presentation), DOCS, XLS (Excel Files), or any file type and embed any documents efficiently. EmbedPress makes embedding PDFs and documents on Gutenberg and Elementor easier with its exclusive ‘Document’ block & addons.

https://youtu.be/1yVbt3XW-Wo

### ⚡ [DISPLAY 3D FLIPBOOK](https://embedpress.com/docs/turn-embedded-pdf-into-a-3d-flip-book/) PDF VIEWER ON YOUR SITE

EmbedPress allows you to embed PDFs and convert them into 3D PDF flipbook with a single click. Make your PDF 3D flipbooks more dynamic to capture viewers' attention immediately. Moreover, you can add a toolbar, sound effects and more to make your PDF 3D flipbook more interactive.

### ⚡ [EMBED POSTS](https://embedpress.com/docs/embed-web-pages-post-embedpress-wrapper/) AND PAGES FROM COUNTLESS WEBSITES

EmbedPress offers a soothing experience to embed pages and posts from your desired website. This feature is called Wrapper. Now you can embed any websites you want within your website. 

### ↗️ DO SOCIAL SHARE OF EMBEDDED CONTENTS

Sharing content directly from your website is now one click away. You can instantly share embedded content from your website on a variety of platforms with EmbedPress. Display your website content easily in your chosen social feeds without using embed codes.

### 🤝 TRANSLATE YOUR EMBEDDED CONTENT WITH MULTILINGUAL PLUGIN WPML

EmbedPress is now certified as a recommended plugin by and 100% compatible with the most popular WordPress Multilingual Plugin, WPML. [Translate your favorite embedding solution](https://embedpress.com/docs/translate-embedpress-wpml/) for WordPress into any and all of your preferred languages with a few, easy steps.

### 🎬 [EMBED VIDEOS](https://embedpress.com/embed-video/) & ENGAGE AUDIENCES

You can seamlessly embed YouTube, Wistia, and Vimeo, but EmbedPress and other 25+ video sources:

-   [Embed YouTube videos](https://embedpress.com/docs/embed-youtube-wordpress/)
-   [Embed Wistia videos](https://embedpress.com/docs/embed-wistia-videos-wordpress/)
-   [Embed Vimeo videos](https://embedpress.com/docs/embed-vimeo-videos-wordpress/)
-   [Animoto embeds](https://embedpress.com/docs/embed-animoto-videos-wordpress/)
-   [College Humor embeds](https://embedpress.com/docs/embed-collegehumor-videos-wordpress/)
-   [Coub embeds](https://embedpress.com/docs/embed-coub-videos-iwordpress/)
-   [Crowd Ranking embeds](https://embedpress.com/docs/embed-crowdranking-polls-wordpress/)
-   [Cloudup embeds](https://embedpress.com/docs/embed-cloudup-videos-images-or-audios-wordpress/)
-   [Clypit embeds](https://embedpress.com/docs/embed-clypit-audio-wordpress/)
-   [Daily Motion embeds](https://embedpress.com/docs/embed-dailymotion-videos-wordpress/)
-   [Funny or Die embeds](https://embedpress.com/docs/embed-funnyordie-videos-wordpress/)
-   [Hulu embeds](https://embedpress.com/docs/embed-hulu-videos-wordpress/)
-   [Kickstarter embeds](https://embedpress.com/docs/embed-kickstarter-videos-wordpress/)
-   [NFB embeds](https://embedpress.com/docs/embed-nfb-videos-wordpress/)
-   [Sapo Videos embeds](https://embedpress.com/docs/embed-sapo-videos-wordpress/)
-   [SproutVideo embeds](https://embedpress.com/docs/how-to-embed-sproutvideo/)
-   [TED embeds](https://embedpress.com/docs/embed-ted-videos-wordpress/)
-   [Twitch embeds](https://embedpress.com/docs/embed-twitch-streams-chat/)
-   [VideoPress embeds](https://embedpress.com/docs/embed-videopress-videos-wordpress/)
-   [Vidyard embeds](https://www.vidyard.com/)
-   [Wave embeds](https://embedpress.com/docs/how-to-embed-wave-videos-in-wordpress/)

Check the video tutorial 👇

https://youtu.be/5UUGEX1Zzs8

### 🎞️ CUSTOMIZE YOUTUBE VIDEO EMBEDS WITH ADVANCED FEATURES [PRO]

- Add Custom Branding by embedding your own customized logo and call-to-action buttons.
- Embed a ‘Subscription Button’ and boost conversion rates for your website & your YouTube channel.
- Show or hide closed captions & enable live chat with every video.
- Allow autoplay, display video annotations and related videos, and much more.

### 🎯 ADD CUSTOM BRANDING WITH MULTIMEDIA EMBEDS [PRO]

Along with YouTube video embeds, EmbedPress also enables you to embed custom logos and call-to-action buttons in other multimedia content to brand them with your website:

- Custom Branding for Vimeo video embeds [PRO]
- Custom Branding for Wistia video embeds [PRO]
- Custom Branding for Twitch stream embeds [PRO]
- Custom Branding for Dailymotion embed [PRO]
- Custom Branding for PDFs & Documents [PRO]

### 🏆 EXCLUSIVE CUSTOMIZATIONS FOR MULTIMEDIA EMBEDS [PRO] 


-   [Vimeo](https://embedpress.com/docs/embed-vimeo-videos-wordpress/): Play your videos on Loop, turn on Auto Pause, enable DNT, and much more.

-   [Wistia](https://embedpress.com/docs/embed-wistia-videos-wordpress/): Start your videos with a Custom Volume with Controls, enable Captions, and Rewind videos when needed.

-   [Twitch](https://embedpress.com/docs/embed-twitch-streams-chat/): Show Chat, enable a Custom Theme & Full Screen Button, and more.

-   [Dailymotion](https://embedpress.com/docs/how-to-configure-settings-for-dailymotion-video-embeds/): Show Logo, display Video Information or Play Controls, and more.

-   [SoundCloud](https://embedpress.com/docs/how-to-configure-settings-for-soundcloud-audio-embeds/): Add a Buy Button or Download Button, enable Share Button, display Username & more.

-   [Spotify](https://embedpress.com/docs/how-to-embed-spotify-artist-follower-widget/): Choose a Player Background Color and embed Artist Follower Buttons.

-   [Custom Video & Audio Player](https://embedpress.com/docs/video-custom-player-controls/): Update the appearance of your embedded audio and videos with advanced settings.

- Lazy Loading: Enable lazy loading for embedding images on your website so they load faster.
-   [Content Protection](https://embedpress.com/docs/add-ep-content-protection-in-embedded-content/): Control the visibility of your embedded content to prevent unauthorized access.

-   [Showcase Ads](https://embedpress.com/docs/how-to-configure-ep-custom-ad/): Showcase ads in embedded content from 150+ sources on your website using images, videos, or direct URLs.

### ☁️ [EMBED GOOGLE SOURCES](https://embedpress.com/embed-google-sources/): DOCS, SHEETS, DRAWINGS, FORMS, SLIDES, MAPS

Embedding Google sources has become easier than ever, whether they're stored in documents, maps, drawings, spreadsheets, or presentations. EmbedPress supports all of these Google Drive options:

-   [Embed Google Docs](https://embedpress.com/docs/google-docs-embed-wordpress/)
-   [Embed Google Forms](https://embedpress.com/docs/google-forms-embed-wordpress/)
-   [Embed Google Maps](https://embedpress.com/docs/google-maps-embed-wordpress/)
-   [Embed Google Drawings](https://embedpress.com/docs/google-drawings-embed-wordpress/)
-   [Embed Google Sheets](https://embedpress.com/docs/embed-google-sheets-wordpress/)
-   [Embed Google Slides](https://embedpress.com/docs/embed-google-slides-wordpress/)

Check the video tutorial 👇

https://youtu.be/3CWysKDRoG4

### 👍 [EMBED SOCIAL FEEDS](https://embedpress.com/embed-social/) SEAMLESSLY & FOSTER SOCIAL PROOFS

With EmbedPress, you can add Facebook posts to WordPress using just a URL. Facebook post embeds are possible but not easy to use via normal methods. EmbedPress is different; it helps you to embed social feeds instantly without any API integration.

-   [Embed Facebook posts, videos](https://embedpress.com/docs/embed-facebook-posts-wordpress/)
-   [Embed Instagram feeds](https://embedpress.com/docs/embed-instagram-wordpress/)
-   [Embed TikTok posts](https://embedpress.com/docs/embed-tiktok-in-wordpress/)
-   [Embed Twitter (X) tweets](https://embedpress.com/docs/embed-twitter-tweets-wordpress/)

Check the video tutorial 👇

https://youtu.be/h8oPwlZ9P5A

### 🎧 [EMBED AUDIOS](https://embedpress.com/embed-audio/): SPOTIFY, SOUNDCLOUD, MIXCLOUD & OTHERS

EmbedPress supports all of these major options for audio on your WordPress site. You can embed audio easily using the URL instead of generating embed codes:

-   [Embed Spotify](https://embedpress.com/docs/embed-spotify-audios-wordpress/)
-   [Embed Boomplay](https://embedpress.com/docs/how-to-embed-boomplay-music/)
-   [Embed MixCloud](https://embedpress.com/docs/embed-mixcloud-audio-wordpress/)
-   [Embed SoundCloud](https://embedpress.com/docs/embed-soundcloud-audio-wordpress/)
-   [Embed HuffDuffer](https://embedpress.com/docs/embed-huffduffer-audios-wordpress/)
-   [Embed Chirbit](https://embedpress.com/docs/embed-chirbit-audio-wordpress/)
-   [Embed Clyp](https://embedpress.com/docs/embed-clypit-audio-wordpress/)
-   [iHeartRadio embeds](https://embedpress.com/docs/how-to-embed-iheartradio-podcasts-in-wordpress/)
-   [ReverbNation embeds](https://embedpress.com/docs/embed-reverbnation-audio-wordpress/)
-   [SmugMug embeds](https://embedpress.com/docs/embed-smugmug-images-wordpress/)
-   [MusicboxManiacs embeds](https://musicboxmaniacs.com/)
-   [SmashNotes embeds](https://embedpress.com/docs/how-to-embed-smash-notes-podcasts-in-wordpress/)
-   [Spreaker embeds](https://embedpress.com/docs/how-to-embed-spreaker-podcasts-in-wordpress/)

### 🖼️ [EMBED IMAGES](https://embedpress.com/embed-image/): DEVIANTART, GIPHY, FLICK, IMGUR, ETC

EmbedPress supports most popular image-hosting sites, including Deviantart, Giphy, Imgur, Flick, and others:

-   [Embed Deviantart](https://embedpress.com/docs/embed-deviantart-image-wordpress/)
-   [Embed Giphy](https://embedpress.com/docs/embed-giphy-gifs-wordpress/)
-   [Embed Flickr](https://embedpress.com/docs/embed-flickr-image-wordpress/)
-   [Embed Imgur](https://embedpress.com/docs/embed-imgur-images-wordpress/)
-   [23hq embeds](https://embedpress.com/docs/embed-23hq-photos-wordpress/)
-   [GettyImages embeds](https://embedpress.com/docs/embed-23hq-photos-wordpress/)
-   [Orbitvu embeds](https://embedpress.com/docs/embed-23hq-photos-wordpress/)
-   [Zoomable embeds](https://embedpress.com/docs/embed-23hq-photos-wordpress/)

### 📊 EMBED CHARTS AND DIAGRAMS

With EmbedPress, you have a wide variety of options for adding charts and diagrams to your WordPress site. We already saw that EmbedPress supports Google Drawings, but here are 5 more options for charts:

-   [AmCharts embeds](https://embedpress.com/docs/embed-amcharts-wordpress/)
-   [Cacoo embeds](https://embedpress.com/docs/embed-cacoo-charts-wordpress/)
-   [ChartBlocks embeds](https://embedpress.com/docs/embed-chartblocks-charts-wordpress/)
-   [CircuitLab embeds](https://embedpress.com/docs/embed-circuitlab-circuit-wordpress/)
-   [Infogram embeds](https://embedpress.com/docs/embed-infogram-charts-wordpress/)
-   [Datawrapper embeds](https://embedpress.com/docs/how-to-embed-datawrapper-data-charts-in-wordpress/)

### 🎦 [EMBED LIVE STREAM VIDEOS](https://embedpress.com/embed-live-stream-sources/): TWITCH, STREAMABLE, ETC

Make your WordPress site more dynamic and embed live streams to boost engagement and earn revenue. This is much simpler now that you can embed live streaming videos, display live chats, and even add a subscription button, thanks to EmbedPress.

-   [Embed Twitch Live](https://embedpress.com/docs/embed-twitch-streams-chat/)
-   [Streamable embed](https://embedpress.com/docs/how-to-embed-streamable-videos-in-wordpress/)
-   [Embed YouTube live](https://embedpress.com/docs/embed-youtube-wordpress/)

Check the video tutorial 👇

https://youtu.be/1F2mPePF3F4

### ✅ MORE SITES SUPPORTED BY EMBEDPRESS

-   [CodePen embeds](https://embedpress.com/docs/embed-codepen-codes-in-wordpress/) (Code)
-   [Codepoints embed](https://embedpress.com/docs/how-to-embed-codepoints-codes-in-wordpress/) (Code)
-   [CodeSandbox embeds](https://embedpress.com/docs/how-to-embed-codesandbox-codes-in-wordpress/) (Code)
-   [DocDroid embeds](https://www.docdroid.net/) (Document & PDF)
-   [Gfycat embeds](https://embedpress.com/docs/how-to-embed-gfycat-gifs-in-wordpress/) (GIF)
-   [GitHub Gist embeds](https://embedpress.com/docs/embed-github-gist-snippets-wordpress/) (Code)
-   [Loom embeds](https://embedpress.com/docs/how-to-embed-loom-in-wordpress/) (Video)
-   [Meetup embeds](https://embedpress.com/docs/embed-meetup-groups-events-wordpress/) (Groups, Events)
-   [PollDaddy embeds](https://embedpress.com/docs/polldaddy-embed-wordpress/) (Polls, Surveys, Quizzes)
-   [Reddit embeds](https://embedpress.com/docs/embed-reddit-post-wordpress/) (Posts, Comments)
-   [ReleaseWire embeds](https://embedpress.com/docs/embed-releasewire-press-releases-wordpress/) (Press releases)
-   [Scribd embeds](https://embedpress.com/docs/embed-scribd-document-wordpress/) (Documents)
-   [Sketchfab embeds](https://embedpress.com/docs/embed-sketchfab-drawings-wordpress/) (Drawings)
-   [SlideShare embeds](https://embedpress.com/docs/embed-slideshare-presentations-wordpress/) (Presentations)
-   [The New York Times embeds](https://embedpress.com/docs/embed-the-new-york-times-news-in-wordpres/) (News)
-   [TVCF embeds](https://embedpress.com/docs/how-to-embed-tvcf-advertisements-in-wordpress/) (Advertisements)
-   [Wordwall embeds](https://embedpress.com/docs/how-to-embed-wordwall-lessons-in-wordpress/) (Online Lessons & Courses)
-   [EduMedia embeds](https://embedpress.com/docs/how-to-embed-edumedia-science-files-in-wordpress/) (Science Files)
-   [Didacte embeds](https://embedpress.com/docs/how-to-embed-didacte-courses-in-wordpress/) (Online Courses)
-   [Zingsoft embeds](https://embedpress.com/docs/how-to-embed-zingsoft-charts-grids-in-wordpress/) (Charts & Grids)
-   [Codepoints embeds](https://embedpress.com/docs/how-to-embed-codepoints-codes-in-wordpress/) (Codes)
-   [Fitapp embed](https://www.fitapp.info/) (Fitness, Health, and Exercises)
-   [ChartBlocks embeds](https://embedpress.com/docs/embed-chartblocks-charts-wordpress/) (charts)
-   [Roomshare embeds](http://roomshare.jp/) (Room Share Listing)

🙌 After reading this feature list, you can probably imagine EmbedPress is the best plugin on the market. So, do you want to unlock the advanced features? [Upgrade to our Pro version.](https://embedpress.com/#pricing)

### 🚀 BACKED BY A TRUSTED TEAM

This embed plugin is brought to you by the team behind [WPDeveloper](https://wpdeveloper.com/), a dedicated marketplace for WordPress, trusted by 5 millions+ happy WordPress users.

### 👨‍💻 DOCUMENTATION AND SUPPORT

- For helpful articles and tutorials, go to our [Documentation](https://embedpress.com/documentation/)
- For video tutorials, go to our [YouTube Playlist](https://www.youtube.com/playlist?list=PLWHp1xKHCfxAVncPuQlAFHTaRl3kO3qBi)
- If you have any more questions, ask on the [Plugin’s Forum](https://wordpress.org/support/plugin/embedpress/)
- For more information about features, FAQs, and documentation, check out our website at [EmbedPress](https://embedpress.com/)


### 💙 LOVED EMBEDPRESS?

- If you love EmbedPress, [rate us on WordPress](https://wordpress.org/support/plugin/embedpress/reviews/?filter=5)
- Join our [Facebook Group](https://www.facebook.com/groups/wpdeveloper.net/)

### 💪 CONTRIBUTE

The full source code is available on [GitHub Repository](https://github.com/WPDevelopers/embedpress). Feel free to contribute or report an issue.

### 🔥 WHAT’S NEXT?

If you like EmbedPress, then consider checking out our other WordPress Plugins:

🔝 [Essential Addons For Elementor](https://essential-addons.com/elementor/) – Most popular Elementor extensions with 2 millions+ active users in the WordPress repository.

🔔 [NotificationX](https://notificationx.com/) – Best Social Proof & FOMO Marketing Solution to increase conversion rates.

⏰ [SchedulePress](https://wordpress.org/plugins/wp-scheduled-posts/) – Complete solution for WordPress Post Scheduling to manage schedules through an editorial calendar & Social Share.

🗒️ [BetterDocs](https://betterdocs.co/) – Best Documentation & Knowledge Base Plugin for WordPress, which also reduces your manual support request.

☁ [Templately](https://wordpress.org/plugins/templately/) – Ultimate Template clouds with 2500+ ready templates for Elementor & Gutenberg along with FREE cloud collaboration.

💰 [Better Payment](https://wordpress.org/plugins/better-payment/) – Create stunning payment forms and manage payments for donations, membership, and products seamlessly.

== Acknowledgements ==

This plugin incorporates code from the following sources:
- [PDF.js by Mozilla](https://github.com/mozilla/pdf.js/): Used for rendering PDF documents.
- [PDF Flipbook WordPress by iberezansky](https://3dflipbook.net/): Used for creating the 3D flipbook effect.
- [Plyr](https://github.com/sampotts/plyr) : Used for displaying video/audio player
- [Glider](http://nickpiscitelli.github.io/Glider.js) : Used for making Carousel layout
- [PublicAlbum](publicalbum.org) : Used for creating a Google Photos gallery player

== Installation ==

= Modern Way: =
1. Go to the WordPress Dashboard "Add New Plugin" section.
2. Search For "EmbedPress".
3. Install, then Activate it.
4. Follow the [Documentation](https://embedpress.com/documentation/)

= Old Way: =
1. Upload `embedpress` to the `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Follow the [Documentation](https://embedpress.com/documentation/)


= Where Can I Get Support? =

You can ask for help via [the EmbedPress contact form](https://embedpress.com/contact/).

= Do I Need Coding Skills To Use EmbedPress? =

Not at all. You can set up everything your team needs without any coding knowledge. We made it super easy.


== Frequently Asked Questions ==

### Where Can I Get Embedding Solutions Support?

You can ask for help via [the EmbedPress contact form](https://embedpress.com/contact/).

### Do I Need Coding Skills To Use EmbedPress?

Not at all. You can set up everything your team needs without any coding knowledge. We made it super easy.

### Can I Embed Pages And Posts Of Any Website Into Embedpress?

Yes, with EmbedPress you can now embed any website. Just copy the page or post URL and paste it into the EmbedPress blocks. This is called EmbedPress Wrapper.

### Do I Need To Integrate API Keys To Showcase Social Feeds On My Website?

No. while you are embedding Facebook, Twitter, TikTok, etc. posts using EmbedPress, just copy the URL and use it directly.

### How To Customize Embedded Content iFrames?

Just after embedding the content into your website using EmbedPress, iFrame customization panel will appear. From there, you can update iFrame height and width.

### How To Create A 3D Flipbook WordPress Post?

With EmbedPress you can easily create PDF flipbooks. First, upload PDF into your website. Then embed PDF with EmbedPress PDF block. From the right side panel you will find the option to create 3D flipbook. Click on it and instantly you will get 3D flipbook PDF viewer.


== Screenshots ==

1. YouTube Embedding Example
2. EmbedPress Screenshot - Dashboard
3. EmbedPress Screenshot - Shortcode
4. EmbedPress Screenshot - Custom Branding
5. EmbedPress Screenshot - Features (Spotify)

== Changelog ==

The format is based on [Keep a Changelog](http://keepachangelog.com/)
and this project adheres to [Semantic Versioning](http://semver.org/).

= [4.2.8] – 2025-06-26 =
- Added: Support for embedding OneDrive documents (DOCX, XLSX)
- Fixed: Google Photos gallery wasn’t syncing properly
- Fixed: Embeds were not working for Flourish and Fite sources
- Few minor bug fixes and improvements.

= [4.2.7] – 2025-06-03 =
- Added: Dynamic Tag Support for JetEngine Custom Fields
- Few minor bug fixes & improvements.

= [4.2.6] – 2025-05-15 =
- Added: Viewer selection (Office/Google) support for documents using Shortcode.
- Added: Option to enable/disable social profiles.
- Fixed: YouTube autoplay issue.
- Few minor bug fixes & improvements.

= [4.2.5] – 2025-04-28 =
- Fixed: Global height and width issue for embedded sources.
- Improved: Minor bug fixes and overall improvements.

= [4.2.4] – 2025-04-24 =
- Fixed: Text domain error in WordPress 6.8
- Improved: Smoother popup pre-loader and animation transitions.
- Added: Support for large-scale images in popup viewer.
- Added: Controls in dashboard for quick support & review option. 
- Fixed: EmbedPress global height settings issue in Elementor.
- Few minor bug fixes and improvements.

= [4.2.3] – 2025-04-09 =
- Fixed: Giphy and Meetup embeds were not showing the correct height and width.
- Fixed: PDF background color was not following the global color setting.
- Fixed: Custom Player had some issues on smaller screens.
- Few minor bug fixes and improvements.

= [4.2.2] – 2025-03-25 =
- Hotfix: Broken popup appearing on the frontend for logged in users.

= [4.2.1] – 2025-03-24 =
- Added: New Google Photos layout options (Grid, Masonry, Justify).
- Fixed: Scrollbar issue in PDF embedding.
- Minor bug fixes & improvements.

= [4.2.0] – 2025-03-04 =
- Added: Support for embedding Canva templates
- Fixed: Google Album responsiveness issue on mobile devices
- Improved: Instagram embedding.
- Few minor bug fixes & improvements.

= [4.1.10] – 2025-02-05 =
- Added: Airtable Embedding Support.
- Improved: Instagram Reel Embedding.
- Fixed: Deprecated CSS class issue in Elementor.
- Fixed: Fatal error occurred due to the redeclaration of the get_user_roles() function.
- Few minor bug fixes & improvements.

= [4.1.9] – 2025-01-22 =
- Added: Flipbook PDF embedding support for the Classic Editor.
- Fixed: Google Maps embedding issues.
- Fixed: Compatibility issue with Essential Addons’ Filterable Video Gallery widget.
- Minor bug fixes and performance enhancements.

= [4.1.8] – 2025-01-07 =
- Fixed: Embedding on Elementor wasn’t working properly. 
- Few minor bug fixes & improvements.

= [4.1.7] – 2025-01-06 =
- Added: Google Photos embedding support
- Fixed: YouTube pagination wasn’t working properly.
- Few minor bug fixes & improvements.

= [4.1.6] – 2024-12-22 =
- Fixed: Composer dependency related issues.
- Fixed: PHP fatal error occurring while using WordPress versions below 5.0.0.
- Few minor bug fixes & improvements.

= [4.1.5] – 2024-12-10 =
- Fixed: Issues with YouTube Live and Playlist functionality.
- Fixed: Missing Instagram controls in Elementor.
- Few minor bug fixes & improvements.

= [4.1.4] – 2024-11-25 =  
- Fixed: Global height & width control issues.
- Fixed: CSS conflict with the Pretty Google Calendar plugin.
- Improved: Security enhancements for better performance.
- Few minor bug fixes and improvements.

= [4.1.3] – 2024-11-13 =  
- Fixed: Custom player switching issues to full-screen mode on iPhone.
- Fixed: Disable “Copy Text” option wasn’t working on Safari browser.
- Fixed: Horizontal scrollbar was appearing in PDF 3D Flipbook view.
- Few minor bug fixes and improvements.

= [4.1.2] – 2024-11-07 =  
- Fixed: Shortcode issues with Beaver Builder.
- Fixed: Undefined error in Gutenberg Editor.
- Fixed: Facebook embedding issues.
- Few minor bug fixes & improvements.

= [4.1.1] – 2024-10-23 =  
- Added: Spreaker embedding support.
- Added: Height/Width unit selection (px/%) for shortcodes.
- Fixed: Height and width issues in the EmbedPress Elementor widget.
- Fixed: Responsive display issues with Google Docs.
- Few minor bug fixes and improvements.

= [4.1.0] – 2024-10-03 =  
- Fixed: Dailymotion video embedding issue in Elementor.
- Fixed: Global Branding settings issue.
- Improved: Custom Ad UI in the dashboard.
- Improved: Security based on PatchStack report.
- Few minor bug fixes and improvements.

= [4.0.14] – 2024-09-12 =  
- Added: Option to update/replace embedded PDFs.
- Improved: Auto-pause for self-hosted audio/video when a new one plays.
- Few minor bug fixes and improvements.

= [4.0.13] – 2024-09-10 =  
- Improved: Refactored codebase for enhanced performance.  
- Few minor bug fixes and improvements.

= [4.0.12] – 2024-08-28 =  
- fixed: Thumbnail size issues in media library.  
- Few minor bug fixes and improvements.

= [4.0.11] – 2024-08-22 =  
- Added: Advanced layouts for Youtube Embed.
- Few minor bug fixes and improvements.

= [4.0.10] – 2024-08-12 =  
- Improved: Security Enhancement reported by PatchStack.
- Few minor bug fixes & improvements.

= [4.0.9] – 2024-08-8 =  
- Improved: Added lazy load option for PDF embeds.
- Enhanced: Security improvements reported by Patchstack (Kinorth).
- Fixed: few minor bugs and overall performance enhancements.

= [4.0.8] – 2024-07-31 =  
- Hotfix: PDF stopped loading. 

= [4.0.7] – 2024-07-31 =  
- Fixed: Youtube Video embedding issue.
- Improved: Embedded PDF's way of loading in frontend.
- Few minor bug fixes & improvements.

= [4.0.6] – 2024-07-16 =  
- Added: WordPress 6.6 compatibility.
- Fixed: PDF Download, Print and Presentation for IOS devices.
- Few minor bug fixes & improvements.

= [4.0.5] – 2024-07-07 =  
- Added: Google viewer for PPTX file.
- Improved: Security Enhancement
- Few minor bug fixes & improvements.

= [4.0.4] – 2024-06-30 =  
- Fixed: Giphy Embedding issues.
- Added: ‘Add image’ controller in EmbedPress PDF.
- Fixed: Conflicts with BetterDocs plugin.
- Few minor bug fixes and improvements.

= [4.0.3] – 2024-06-12 =  
- Added: 3D FlipBook viewer for Embedded PDF.
- Fixed: PHP 8.3 deprecated issues.
- Fixed: Conflict with Toolset plugin.
- Fixed: Custom player wasn’t showing after embedding MP3 files.
- Fixed: Custom player thumbnail issues for self-hosted video.
- Improved: Plugin’s performance optimization.
- Improved: String translation.
- Improved: Security Enhancement (Reported by PatchStack).
- Few minor bug fixes & improvements.

= [4.0.2] – 2024-06-04 =  
- Fixed: Embedded video's issues with Custom Player.
- Fixed: PDF’s Save & Print button weren’t working on small devices
- Fixed: Current page preview wasn’t working for Embeded PDF.
- Fixed: Security Enhancement (Reported by Wordfence).
- Improved: Global settings support for height, width, and PDF custom color.
- Few minor bug fixes & improvements.

= [4.0.1] – 2024-05-26 =  
- Added: LinkedIn post embedding support.
- Fixed: Design broken issues with latest Elementor.
- Fixed: X post embedding issues.
- Fixed: Conflict with WooCommerce product editor panel.
- Improved: Optimized code for better performance.
- Few minor bug fixes & improvements.

= [4.0.0] – 2024-05-21 =  
- Added: Instagram feed embedding support with advanced layouts.
- Improved: EP dashboard UI.
- Few minor bug fixes & improvements.

= [3.9.17] – 2024-05-07 =
- Fixed: Custom thumbnail wasn’t working properly on Custom player.
- Fixed: Custom player conflicts with Scisco theme.
- Fixed: Custom thumbnail wasn’t showing on Youtube video.
- Improved: Security Enhancement (Reported by Wordfence).
- Few minor bug fixes & improvements.

= [3.9.16] – 2024-04-16 =
- Fixed: Global CSS breaks page layout.
- Fixed: Hight width controller for Matterport Video embedding.
- Fixed: Responsiveness issue for iPhone navigation. 
- Improved: Optimized performance & Security.
- Few minor bug fixes & improvements.

= [3.9.15] – 2024-04-04 =
- Fixed: Embeded PDF fullscreen mode wasn't working for iPhone.
- Improved: Security Enhancement.
- Few minor bug fixes & improvements.

= [3.9.14] – 2024-03-28 =
- Fixed: License activation conflicts with Classic editor.
- Fixed:  Zoom in/out wasn’t working in PDF Presentation mode.
- Few minor bug fixes & improvements.

= [3.9.13] – 2024-03-21 =
- Fixed : Gutenberg editor responsive issues.
- Improved: Security patch updated.
- Improved:  PDF controls for Elementor editor.
- Few minor bug fixes and improvements. 

= [3.9.12] – 2024-03-12 =
- Added: Security Enhancement. 
- Fixed: License activation issues with page reload. 
- Few minor bug fixes and improvements. 

= [3.9.11] – 2024-03-5 =
- Fixed: OpenSea NFT collection/assets embedding issues.
- Fixed: Auto Play wasn’t working for Youtube Live Stream
- Fixed: php fatal errors with: _esc_html__()
- Updated: Security patch updated for better performance.
- Improved: Documents block/widget controls.
- Few minor bug fixes & improvements.

= [3.9.10] – 2024-02-19 =
- Added: Security Enhancement.
- Few minor bug fixes & improvements.

= [3.9.9] – 2024-02-14 =
- Improved: Security patch updated for better performance.
- Few minor bug fixes & improvements.

= [3.9.8] – 2024-02-01 =
- Fixed: YouTube live stream wasn't embedding.
- Fixed: PHP 8.2 depricated issue.
- Fixed: Auto-play control of Tiktok embedding.
- Fixed: SoundCloud settings page wasn't working.
- Fixed: '.dk' domain URL wasn't embedding.
- Improved: OpenSea embedding. 
- Few minor bug fixes & improvements.

= [3.9.7] – 2024-01-09 =
- Added: eSpatial Map embedding support.
- Fixed: YouTube Embedding issues.
- Fixed: PDF Embed wasn't showing for Logged out users.
- Fixed: Custom Branding logo goes big in YouTube videos.
- Fixed: Deprecated issues with PHP 8.1
- Few minor bug fixes & improvements.

= [3.9.6] - 2023-12-27 =
- Improved: Security Enhancement
- Few minor bug fixes & improvements.

= [3.9.5] - 2023-12-07 =
- Fixed: Instagram height width controller wasn’t working.
- Improved: Updated security patch for better performance | Suggested by PatchStack.
- Fixed: Conflict with GeneratePress theme and Generate block.
- Few minor bug fixes & improvements.

= [3.9.4] - 2023-11-23 =
- Improved: Security enhancement for better performance.
- Few minor bug fixes & improvements.

= [3.9.3] - 2023-11-20 =
- Few minor bug fixes & improvements.

= [3.9.2] - 2023-11-16 =
- Fixed: Conflicts with Essential Blocks for Gutenberg. 
- Improved: Security Enhancement | Suggested by WPScan. 
- Few minor bug fixes & improvements.

= [3.9.1] - 2023-10-22 =
- Added: YouTube Shorts controls for Gutenberg.
- Added: Office file embedding support for Classic editor.
- Improved: PDF controls for Elementor.
- Fixed: OpenSea API related issues.
- Fixed: NFT gallery overlapping in mobile devices.
- Few minor bug fixes & improvements.

= [3.9.0] - 2023-10-05 =
- Revamped: EmbedPress Dashboard.
- Fixed: Conflict with RankMath.
- Few minor bug fixes & improvements.


[See changelog for all versions.](https://embedpress.com/changelog)


== Upgrade Notice ==

* [Major Update] Must Update.
