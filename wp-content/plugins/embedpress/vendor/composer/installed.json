{"packages": [{"name": "priyomukul/wp-notice", "version": "v2.x-dev", "version_normalized": "2.9999999.9999999.9999999-dev", "source": {"type": "git", "url": "**************:priyomukul/wp-notice.git", "reference": "f3d02f6e772cb459e9b89d77605e02646f9c5d65"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/priyomukul/wp-notice/zipball/f3d02f6e772cb459e9b89d77605e02646f9c5d65", "reference": "f3d02f6e772cb459e9b89d77605e02646f9c5d65", "shasum": ""}, "time": "2023-11-20T08:03:13+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"PriyoMukul\\WPNotice\\": "src/"}}, "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "support": {"source": "https://github.com/priyomukul/wp-notice/tree/v2", "issues": "https://github.com/priyomukul/wp-notice/issues"}, "install-path": "../priyomukul/wp-notice"}, {"name": "wpdevelopers/embera", "version": "2.0.17", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/WPDevelopers/Embera", "reference": "5f521fbe77fdfc992dc399ee39e5fdb338ea1f04"}, "require": {"ext-json": "*", "php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^8.3"}, "suggest": {"ext-curl": "Allow to fetch data using curl instead of using file_get_contents"}, "time": "2021-01-28T07:14:10+00:00", "type": "library", "installation-source": "source", "autoload": {"psr-4": {"Embera\\": "src/Embera"}}, "autoload-dev": {"psr-4": {"Embera\\": "tests/Embera"}}, "scripts": {"test": ["phpunit"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.michael-pratt.com", "role": "Author/Developer"}], "description": "Oembed consumer library. Converts urls into their html embed code. Supports 150+ sites, such as Youtube, Twitter, vimeo, Instagram etc.", "homepage": "https://github.com/WPDevelopers/Embera", "keywords": ["Auto embed", "Embed", "Embed Text", "Instagram", "<PERSON><PERSON><PERSON>", "Responsive Embeds", "Twitter", "<PERSON><PERSON>", "Vimeo", "Vine", "Youtube"], "install-path": "../wpdevelopers/embera"}], "dev": true, "dev-package-names": []}