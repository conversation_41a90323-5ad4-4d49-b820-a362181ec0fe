<?php
/**
 * PolariShare.php
 *
 * @package Embera
 * <AUTHOR> <<EMAIL>>
 * @link   http://www.michael-pratt.com/
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Embera\Provider;

use Embera\Url;

/**
 * PolariShare Provider
 * @link https://polarishare.com
 */
class PolariShare extends ProviderAdapter implements ProviderInterface
{
    /** inline {@inheritdoc} */
    protected $endpoint = 'https://api.polarishare.com/rest/api/oembed?format=json';

    /** inline {@inheritdoc} */
    protected static $hosts = [
        'polarishare.com'
    ];

    /** inline {@inheritdoc} */
    protected $httpsSupport = true;

    /** inline {@inheritdoc} */
    protected $responsiveSupport = true;

    /** inline {@inheritdoc} */
    public function validateUrl(Url $url)
    {
        return (bool) (preg_match('~polarishare\.com/([^/]+)/([^/]+)(/[^/]+)?$~i', (string) $url));
    }

    /** inline {@inheritdoc} */
    public function normalizeUrl(Url $url)
    {
        $url->convertToHttps();
        $url->removeQueryString();
        $url->removeLastSlash();

        return $url;
    }

}
