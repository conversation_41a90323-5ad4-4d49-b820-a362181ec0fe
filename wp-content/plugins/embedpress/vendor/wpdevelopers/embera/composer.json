{"name": "wpdevelopers/embera", "type": "library", "description": "Oembed consumer library. Converts urls into their html embed code. Supports 150+ sites, such as Youtube, Twitter, vimeo, Instagram etc.", "keywords": ["Embed", "<PERSON><PERSON><PERSON>", "Auto embed", "Embed Text", "<PERSON><PERSON>", "Responsive Embeds", "Youtube", "Vimeo", "Vine", "Twitter", "Instagram"], "homepage": "https://github.com/WPDevelopers/Embera", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.michael-pratt.com", "role": "Author/Developer"}], "require": {"php": ">=5.6", "ext-json": "*"}, "require-dev": {"phpunit/phpunit": "^8.3"}, "suggest": {"ext-curl": "Allow to fetch data using curl instead of using file_get_contents"}, "autoload": {"psr-4": {"Embera\\": "src/Embera"}}, "autoload-dev": {"psr-4": {"Embera\\": "tests/Embera"}}, "scripts": {"test": "phpunit"}}