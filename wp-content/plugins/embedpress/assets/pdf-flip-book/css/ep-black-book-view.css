:root {
  --body-bg-color: rgba(212, 212, 215, 1);
  --toolbar-bg-color: rgb(249 249 250);
  --toolbar-icon-color: rgb(0 0 0);
  --toolbar-text-color: rgb(0 0 0);
  --toolbar-active-icon-bg-color: rgb(221 222 223);
  --toolbar-item-hover-bg-color: rgb(221 222 223);
  --toolbar-current-page-number-bg-color: #ddd;
  --toolbar-page-number-bg-color: #ccc;

}

@media (prefers-color-scheme:dark) {
  :root {
    --body-bg-color: rgba(42, 42, 46, 1);
    --toolbar-bg-color: #3c3c3c;
    --toolbar-icon-color: #fff;
    --toolbar-text-color: #fff;
    --toolbar-active-icon-bg-color: #222;
    --toolbar-item-hover-bg-color: #333;
    --toolbar-current-page-number-bg-color: #222;
    --toolbar-page-number-bg-color: #333;


  }
}

[ep-data-theme=light] {
  --body-bg-color: rgba(212, 212, 215, 1);
  --toolbar-bg-color: rgb(249 249 250);
  --toolbar-icon-color: rgb(0 0 0);
  --toolbar-active-icon-bg-color: rgb(221 222 223);
  --toolbar-item-hover-bg-color: rgb(221 222 223);
  --toolbar-text-color: rgb(0 0 0);
  --toolbar-current-page-number-bg-color: #ddd;
  --toolbar-page-number-bg-color: #ccc;
}

[ep-data-theme=dark] {
  --body-bg-color: rgba(42, 42, 46, 1);
  --toolbar-bg-color: #3c3c3c;
  --toolbar-icon-color: #fff;
  --toolbar-text-color: #fff;
  --toolbar-active-icon-bg-color: #222;
  --toolbar-item-hover-bg-color: #333;
  --toolbar-current-page-number-bg-color: #222;
  --toolbar-page-number-bg-color: #333;

}

.flip-book {
  position: relative;
  height: 100%;
  background-color: var(--body-bg-color);
}

.flip-book .view {
  text-align: center;
  height: 100%;
}


.flip-book .view .prev, .flip-book .view .next {
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  z-index: 1;
}

.flip-book .view .prev {
  left: 20px;
}

.flip-book .view .next {
  right: 20px;
}


.flip-book .view .fnav a {
  font-size: 72pt;
  color: #666;
}

.flip-book .view .fnav a:hover {
  cursor: pointer;
  text-shadow: 0 0 7px #000;
}

.flip-book .view .fnav a:active {
  font-size: 70pt;
}

.flip-book .view .fnav .hidden {
  display: none;
}

.flip-book .view .fnav .active {
  color: #555;
}

.flip-book .view .fnav .disabled {
  color: #aaa;
}

.flip-book .view .fnav .disabled:hover {
  cursor: not-allowed;
  text-shadow: none;
}

.flip-book .view .loading-progress {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.flip-book .view .loading-progress.hidden {
  display: none;
}

.flip-book .loading-progress .progress {
  background-color: #555;
  background-image: none;
  width: 50px;
  height: 50px;
  margin: 0 auto;
  border-radius: 30px;
  padding: 5px;
  box-shadow: 0px 0px 3px var(--body-bg-color);
  margin-bottom: 10px;
}

.flip-book .loading-progress .progress::after {
  content: ' ';
  display: block;
  width: 100%;
  height: 100%;
  background-size: contain;
  background-image: url('../images/light-loader.gif');
}

.flip-book .page-loading {
  width: 50px;
  height: 50px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.flip-book .page-loading.hidden {
  display: none;
}

.flip-book .page-loading::after {
  content: ' ';
  display: block;
  width: 100%;
  height: 100%;
  background-size: contain;
  background-image: url('../images/light-loader.gif');
}

.flip-book .loading-progress .caption {
  background-color: #555;
  border: 1px solid var(--toolbar-item-hover-bg-color);
  padding: 7px 10px;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 10pt;
  border-radius: 5px;
  font-style: italic;
  color: #eee;
}

.flip-book .controls {
  position: relative;
}

/* .flip-book .controls .fnavbar {
  margin: 0 auto;
  opacity: 0.3;
  transition: opacity 2s ease-in-out 3s;
}

.flip-book .controls .fnavbar:hover {
  opacity: 1;
  transition: opacity 1s ease;
} */

.flip-book .controls .ctrl {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  position: absolute;
  bottom: 10px;
}

.ctrl ul {
  margin: 0;
  padding: 0;
}

.ctrl .fnavbar {
  text-align: center;
  /* background-image: linear-gradient(to bottom,#3c3c3c 0,#222 100%); */
  background-repeat: repeat-x;
  padding: 3px;
  overflow: visible;
  background-color: var(--toolbar-bg-color);

  border-radius: 4px;
}

.ctrl .fnavbar a {
  text-decoration: none;
  font-size: 14px;
}

.ctrl .fnavbar .fnav {
  font-size: 0;
  display: flex;
  text-align: left;
  gap: 10px;
}

.ctrl .fnavbar .fnav li {
  font-size: 12pt;
  margin: 0;
  width: auto;
}

.ctrl .fnavbar .fnav .fnav-item {
  display: flex !important;
  align-items: center;
  justify-content: center;
}

.ctrl .fnavbar .fnav .hidden {
  display: none;
}

.ctrl .fnavbar .fnav .fnav-item>a {
  /* padding: 10px; */
  display: inline-block;
  color: #ccc;
}

.ctrl .fnavbar .fnav .fnav-item>a {
  /* padding: 10px; */
  display: inline-block;
  color: #ccc;
  width: 30px;
  display: inline-block;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ctrl .fnavbar .fnav>.active>a {
  color: #fff;
}

.ctrl .fnavbar .fnav .fnav-item>a:hover {
  color: #fff;
  cursor: pointer;
}

.ctrl .fnavbar .fnav .fnav-item>a:active {
  text-shadow: 0 0 2px #fff;
}

.ctrl .fnavbar .fnav>.disabled>a {
  color: #888;
}

.ctrl .fnavbar .fnav>.disabled>a:hover {
  color: #888;
  cursor: not-allowed;
}

.ctrl .fnavbar .fnav>.active a {
  background: var(--toolbar-active-icon-bg-color);
  border-radius: 5px;
}

.ctrl .fnavbar .fnav .dropdown, .ctrl .fnavbar .fnav .dropup {
  position: relative;
}

.ctrl .fnavbar .fnav .dropdown .menu {
  top: 100%;
  box-shadow: 3px 0 3px var(--toolbar-bg-color);
}

.ctrl .fnavbar .fnav .icon-caret {
  width: 26px;
  display: inline-block;
}

.ctrl .fnavbar .fnav .dropdown .caret {
  display: inline-block;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #ccc;
}

.ctrl .fnavbar .fnav .dropup .menu {
  box-shadow: 3px 0 3px var(--toolbar-bg-color);
}

.ctrl .fnavbar .fnav .dropup .caret {
  display: inline-block;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 5px solid #ccc;
}

.ctrl .fnavbar .menu {
  position: absolute;
  border: 1px solid var(--toolbar-item-hover-bg-color);
  background-color: var(--toolbar-bg-color);
  z-index: 10;
  min-width: 160px;
}

.ctrl .fnavbar .menu li {
  display: block;
  padding: 5px 10px;
}

.ctrl .fnavbar .menu .divider {
  height: 1px;
  background-color: var(--toolbar-item-hover-bg-color);
  padding: 0;
  margin: 5px 0;
}

.ctrl .fnavbar .menu .active {
  background-color: var(--toolbar-active-icon-bg-color);
}

.ctrl .fnavbar .menu li:hover {
  background-color: var(--toolbar-item-hover-bg-color);
  cursor: pointer;
}

.ctrl .fnavbar .menu li a {
  color: var(--toolbar-text-color);
}

.ctrl .fnavbar .menu .disabled a {
  color: #aaa;
  cursor: not-allowed;
}

.ctrl .fnavbar .menu .icon {
  display: inline-block;
  width: 14px;
  height: 14px;
  text-align: center;
}

.ctrl .pages {
  padding: 8px 10px;
  display: flex;
  font-size: 0;
}

.ctrl .pages .number, .ctrl .pages .amount {
  width: 50px;
  height: 20px;
  text-align: center;
  display: inline-block;
  font-size: 11pt;
  border: 0;
  color: var(--toolbar-text-color);
  background-color: var(--toolbar-page-number-bg-color);
  font-weight: bold;
  border-radius: 0;
  margin: 0;
  padding: 2px;
}

.ctrl .pages .number {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  background-color: var(--toolbar-current-page-number-bg-color);
}

.ctrl .pages .amount {
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}

/* floating window */

.flip-book .float-wnd {
  position: absolute;
  top: 10px;
  left: 10px;
  border-radius: 5px 5px 0 0;
  background-color: #1a1a1a;
  width: 300px;
  z-index: 1;
  box-shadow: 3px 0 3px var(--toolbar-toolbar-bg-color);
}

.flip-book .float-wnd.hidden {
  display: none;
}

.flip-book .float-wnd .header {
  border-radius: 4px 4px 0 0;
  background-color: var(--toolbar-item-hover-bg-color);
  background-repeat: repeat-x;
  padding: 7px 10px;
  border: 1px solid var(--toolbar-item-hover-bg-color);
  border-bottom: none;
  color: #fff;
  font-weight: bold;
  cursor: move;
  font-family: sans-serif;
  font-size: 15px;
  display: flex;
  align-items: center;
}

.flip-book .float-wnd .header .close {
  right: 8px;
  font-size: 14px;
  position: absolute;
  color: #ccc;
}

.flip-book .float-wnd .header .close svg {
  height: 10px;
}

.flip-book .float-wnd .header .close:hover {
  color: #fff;
}

.flip-book .float-wnd .header .close:active {
  font-size: 12px;
  padding-top: 1px;
  padding-right: 1px;
}

.flip-book .float-wnd .body {
  border: 1px solid var(--toolbar-item-hover-bg-color);
  border-top: none;
}

/* toc */

.ctrl .toc {
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background-color: var(--body-bg-color);
}

.ctrl .toc a {
  text-decoration: none;
}

.ctrl .toc .hidden {
  display: none;
}

.ctrl .toc .toc-menu {
  padding: 8px 10px;
}

.ctrl .toc .toc-menu ul {
  list-style: none;
  padding: 0;
}

.ctrl .toc .toc-menu li {
  display: inline-block;
  padding-right: 5px;
}

.ctrl .toc .toc-menu a {
  color: #ccc;
  font-size: 12px;
}

.ctrl .toc .toc-menu a:hover {
  color: #fff;
}

.ctrl .toc .toc-menu a:active {
  text-shadow: 0 0 2px #fff;
}

.ctrl .toc .toc-menu .active a {
  color: #fff;
}


.ctrl .toc .toc-view {
  padding: 10px 0;
  max-height: 80vh;
  overflow-y: auto;
  overflow-x: hidden;
}

.ctrl .toc .toc-view::-webkit-scrollbar {
  width: 7px;
  height: 7px;
}

.ctrl .toc .toc-view::-webkit-scrollbar-button {
  width: 0;
  height: 0;
}

.ctrl .toc .toc-view::-webkit-scrollbar-thumb {
  background: #4a4a4a;
  border-radius: 2px;
}

.ctrl .toc .toc-view::-webkit-scrollbar-thumb:hover {
  background: #3a3a3a;
}

.ctrl .toc .toc-view::-webkit-scrollbar-thumb:active {
  background: #5a5a5a;
}

.ctrl .toc .toc-view::-webkit-scrollbar-track {
  background: #2a2a2a;
  border-left: 2px solid #1a1a1a;
  border-right: 2px solid #1a1a1a;
}

.ctrl .bookmarks .white-space, .ctrl .bookmarks .togle, .ctrl .bookmarks .togle i {
  width: 18px;
  height: 18px;
}

.ctrl .bookmarks .white-space {
  display: inline-block;
}

.ctrl .bookmarks li {
  width: 10000px;
}

.ctrl .bookmarks .item .area {
  padding: 2px 0;
}

.ctrl .bookmarks .level-0 .area {
  padding-left: 5px;
}

.ctrl .bookmarks .level-1 .area {
  padding-left: 10px;
}

.ctrl .bookmarks .level-2 .area {
  padding-left: 15px;
}

.ctrl .bookmarks .level-3 .area {
  padding-left: 20px;
}

.ctrl .bookmarks .level-4 .area {
  padding-left: 25px;
}

.ctrl .bookmarks .item .area:hover {
  background-color: var(--toolbar-item-hover-bg-color);
}

.ctrl .bookmarks .item .area:active {
  background-color: var(--toolbar-item-hover-bg-color);
}

.ctrl .bookmarks .item a {
  color: #fff;
  font-size: 14px;
}

.ctrl .bookmarks ul {
  list-style: none;
  padding: 0;
}

.ctrl .bookmarks .togle {
  display: inline-block;
  text-align: center;
  position: relative;
}

.ctrl .bookmarks .togle::before {
  content: ' ';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  border: 0px solid #222;
  transition: border 0.2s;
}

.ctrl .bookmarks .togle:active::before {
  border: 12px solid #222;
  transition: border 0.05s;
}

.ctrl .bookmarks .togle i {
  transform: rotateZ(0deg);
  transition: transform 0.2s;
}

.ctrl .bookmarks .togle.minimized i {
  transform: rotateZ(90deg);
  transition: transform 0.2s;
}


.ctrl .thumbnails .item a {
  color: #fff;
  font-size: 14px;
}

.ctrl .thumbnails {
  padding: 10px 0;
  text-align: center;
}

.ctrl .thumbnails .thumbnail {
  height: 170px;
  margin-bottom: 5px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.ctrl .thumbnails .loading {
  position: relative;
}

.ctrl .thumbnails .loading::after {
  content: ' ';
  position: absolute;
  width: 40px;
  height: 40px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background-size: contain;
  background-image: url('../images/light-loader.gif');
}

.ctrl .thumbnails .item {
  display: inline-block;
  width: 128px;
  padding: 5px 5px;
  border: 1px solid transparent;
  border-radius: 3px;
}

.ctrl .thumbnails .item:hover {
  border: 1px solid #555;
  background-color: var(--toolbar-item-hover-bg-color);
}

.ctrl .thumbnails .heading {
  overflow: hidden;
  height: 20px;
}


.ctrl .search .result {
  padding: 7px 10px;
  cursor: pointer;
}

.ctrl .search .result:hover {
  background-color: var(--toolbar-item-hover-bg-color);
}

.ctrl .search .results a {
  color: #fff;
  font-size: 14px;
}

.ctrl .search .query {
  padding: 0 10px;
  padding-bottom: 10px;
}

.ctrl .search .query input {
  width: 100%;
}

.ctrl .search .status {
  text-align: center;
  color: #ccc;
  font-size: 12px;
}


span.icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

span.icon svg {
  width: auto;
  height: 14px;
}

span.icon svg path {
  fill: var(--toolbar-icon-color);
}


.icon-caret svg {
  margin-right: 2px;
}

span.icon .rotate-180 {
  transform: rotate(180deg);
}

span.icon.arrow-icon svg, .sound-icon svg {
  width: 14px !important;
}

li.dropup.fnav-item.toggle.widSettings ul.menu {
  border-radius: 6px;
  bottom: 38px;
  min-height: 60px;
  overflow: hidden;
}

li.dropup.fnav-item.toggle.widSettings ul.menu li a {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 13px;
  line-height: 1.6em;
}

span.icon.next-page-icon svg, span.icon.previous-page-icon svg {
  height: 45px;
  width: auto;
}

.ctrl .fnavbar .fnav .hidden.but-show {
  display: inline;
}

.hidden.but-show {
  display: inline !important;
}

li.fnav-item.disabled {
  opacity: 0.5;
}

.controls.toolbar-position-top {
  top: calc(-100% + 60px) !important;
}

.controls.toolbar-position-top ul.menu {
  top: 40px;
}

.controls.toolbar-position-bottom ul.menu {
  bottom: 100%;
}

@media only screen and (max-width: 680px) {
  .ctrl .fnavbar .fnav li.fnav-item.cmdToc, .ctrl .fnavbar .fnav li.fnav-item.cmdForward, .ctrl .fnavbar .fnav li.fnav-item.cmdBackward, .ctrl .fnavbar .fnav li.fnav-item.cmdDefaultZoom, .ctrl .fnavbar .fnav li.fnav-item.cmdPrint {
    display: none !important;
  }

  iframe.embedpress-embed-document-pdf.embedpress-pdf-1718089021903 {
    max-height: 380px;
  }

  .flip-book .controls {
    transform: scale(.8);
  }

  .flip-book .controls.toolbar-position-top {
    top: calc(-100% + 45px) !important;
  }

  .next span.icon svg path, .prev span.icon svg path {
    fill: #888;
  }

  .next span.icon svg, .prev span.icon svg {
    height: 30px;
  }

  li.dropup.fnav-item.toggle.widSettings ul.menu {
    right: 0;
  }

}