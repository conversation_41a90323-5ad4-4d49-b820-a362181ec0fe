/**
 * @package     EmbedPress
 * <AUTHOR> <<EMAIL>>
 * @copyright   Copyright (C) 2018 EmbedPress. All rights reserved.
 * @license     GPLv2 or later
 * @since       1.0
 */
.embedpress-wrapper {
    position: relative;
}

.ose-dailymotion.responsive,
.ose-kickstarter.responsive,
.ose-rutube.responsive,
.ose-ted.responsive,
.ose-vimeo.responsive,
.ose-youtube.responsive,
.ose-ustream.responsive,
.ose-google-docs.responsive,
.ose-animatron.responsive,
.ose-amcharts.responsive,
.ose-on-aol-com.responsive,
.ose-animoto.responsive,
.ose-soundcloud.responsive,
.ose-videojug.responsive,
.ose-facebook.responsive,
.ose-issuu.responsive {
    overflow: hidden;
    position: relative;
    height: auto;
}

.ose-dailymotion.responsive iframe,
.ose-kickstarter.responsive iframe,
.ose-rutube.responsive iframe,
.ose-ted.responsive iframe,
.ose-vimeo.responsive iframe,
.ose-vine.responsive iframe,
.ose-youtube.responsive iframe,
.ose-ustream.responsive iframe,
.ose-google-docs.responsive iframe,
.ose-animatron.responsive iframe,
.ose-amcharts.responsive iframe,
.ose-on-aol-com.responsive iframe,
.ose-animoto.responsive iframe,
.ose-soundcloud.responsive iframe,
.ose-videojug.responsive iframe,
.ose-issuu.responsive iframe {
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    position: absolute;
}

/* 16:9 aspect ratio */
.ose-dailymotion.responsive,
.ose-kickstarter.responsive,
.ose-rutube.responsive,
.ose-ted.responsive,
.ose-vimeo.responsive,
.ose-youtube.responsive,
.ose-animatron.responsive,
.ose-amcharts.responsive,
.ose-on-aol-com.responsive,
.ose-animoto.responsive,
.ose-videojug.responsive {
    padding-bottom: 56.25%;
}

/* 1:1 aspect ratio */
.ose-vine.responsive {
    padding-bottom: 100%;
}

/* Mixed aspect ratio */
.ose-ustream.responsive {
    padding-bottom: 63.3%;
}

.ose-google-docs.responsive {
    padding-bottom: 62.6%;
}

.ose-google-docs.ose-google-docs-drawings.responsive {
    padding-bottom: 74.5%;
}

.ose-google-docs.ose-google-docs-document.responsive,
.ose-google-docs.ose-google-docs-forms.responsive,
.ose-google-docs.ose-google-docs-spreadsheets.responsive {
    padding-bottom: 142%;
}

.ose-soundcloud.responsive {
    padding-bottom: 155px;
}

.ose-issuu.responsive iframe {
    z-index: 2;
}

.ose-issuu.responsive {
    padding-bottom: 31.25%;
}

.ose-issuu>div>div>div:last-child {
    width: 100% !important;
    z-index: 0;
    position: absolute;
    bottom: 0;
}

.ose-mixcloud.responsive iframe {
    width: 100%;
}

/* Fix scrolling on iOS devices */
.ose-google-docs.responsive {
    -webkit-overflow-scrolling: touch;
    overflow-y: auto;
}

.ose-facebook.responsive iframe {
    padding-bottom: 0;
    width: 100%;
}

.elementor-widget iframe {
    max-height: 100% !important;
}

.elementor-cbutton-preview-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 100px 30px;
    background: #fbf6f6;
}

@media only screen and (max-width: 800px) {

    .elementor-widget-embedpres_elementor .embedpress-facebook-vid-iframe,
    .embedpress-gutenberg-wrapper .embedpress-facebook-vid-iframe,
    .embedpress-facebook-vid-iframe {
        max-height: inherit !important;
    }

    .ep-first-video iframe {
        max-height: 100% !important;
    }

    .ep-youtube__content__block .youtube__content__body .content__wrap:not(.youtube-carousel) {
        grid-template-columns: repeat(auto-fit, minmax(calc(50% - 30px), 1fr)) !important;
    }
}

@media only screen and (max-width: 500px) {

    .elementor-widget-embedpres_elementor .embedpress-facebook-vid-iframe,
    .embedpress-gutenberg-wrapper .embedpress-facebook-vid-iframe,
    .embedpress-facebook-vid-iframe {
        max-height: 390px !important;
    }

    .ep-youtube__content__block .youtube__content__body .content__wrap:not(.youtube-carousel) {
        grid-template-columns: repeat(auto-fit, minmax(calc(100% - 30px), 1fr)) !important;
    }
}

.ose-matterport .embera-embed-responsive, .ose-matterport .embera-embed-responsive iframe {
    width: 100%;
    height: 100%;
}

.ose-deviantart img, .ose-deviantart a, .ose-deviantart div {
    width: 100%;
    height: 100%;
}

/*Meetup Event styling starts */
.ose-meetup {
    overflow: auto !important;
}

.ose-meetup img {
    height: auto;
}

article.embedpress-event div#sponsors {
    display: none;
}

.embedpress-event .link {
    color: #0098ab;
}

.embedpress-event .visibility--a11yHide {
    border: 0;
    clip: rect(0 0 0 0);
    position: absolute;
    overflow: hidden;
    margin: -1px;
    padding: 0;
    width: 1px;
    height: 1px;
}

.embedpress-event .text--small {
    font-size: 14px;
    margin: 0;
}

.embedpress-event .flex {
    display: flex;
    box-sizing: border-box;
}

.embedpress-event .flex--wrap {
    flex-wrap: wrap;
}

.embedpress-event .flex--row {
    align-items: center;
    box-sizing: border-box;
    display: flex;
    justify-content: flex-start;
    flex-direction: row;
    flex-wrap: nowrap;
}

.embedpress-event .flex-item {
    flex-basis: 0;
    flex-grow: 1;
    width: auto;
    min-width: 0;
    /*padding-left: 16px;*/
    box-sizing: border-box;
}

.embedpress-event .flex-item--shrink {
    flex-basis: auto;
    -webkit-box-flex: 0;
    flex-grow: 0;
    flex-shrink: 0;
    width: auto;
}

.embedpress-event .flex--row>.flex-item:first-child {
    padding-left: 0;
}

.embedpress-event .text--bold {
    font-weight: 700;
}

.embedpress-event h1,
.embedpress-event h2,
.embedpress-event h3,
.embedpress-event h4,
.embedpress-event h5,
.embedpress-event h6 {
    font-size: inherit;
}

.embedpress-event .ep-event--title {
    font-size: 32px;
    font-weight: 700;
}

.embedpress-event .ep-event--date {
    color: #757575;
    font-weight: 400;
    font-size: 16px;
}

/*Host*/
.embedpress-event .ep-event--host {
    margin-top: 20px;
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.ep-event--host .avatar-print {
    border-radius: 50%;
    height: 50px;
    width: 50px;
}

.embedpress-event img.avatar--person {
    background-image: none !important;
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: cover;
    border-radius: 50%;
    box-sizing: border-box;
    vertical-align: middle;
}

.event-info-hosts-text {
    padding-left: 20px;
    font-size: 16px;
    font-weight: 400;
}

.embedpress-event .event-description {
    margin-top: 20px;
}

.text--sectionTitle {
    font-size: 20px;
    line-height: 28px;
}

.ep-event--attendees {
    margin-top: 50px;
}

.ep-event--attendees>.flex {
    margin-bottom: 20px;
}

.ep-event--attendees .gridList {
    list-style: none;
    margin: 0 -16px 0 0;
    padding: 0;
}

.ep-event--attendees .gridList-item {
    width: auto;
}

.ep-event--attendees .gridList--autoHeight--has4>.gridList-item {
    flex: 0 0 50%;
    max-width: 50%;
}

.ep-event--attendees .groupMember-name {
    line-height: 1.2 !important;
}

.ep-event--attendees .avatar--person {
    margin-bottom: 15px;
    display: inline-block;
    border-radius: 50%;
}

.ep-event--attendees img.avatar-print {
    border-radius: 50%;
}

.ep-event--attendees .groupMember-role {
    font-size: 12px;
    color: #757575;
    padding-top: 2px;
    margin: 0;
}

.ep-event--attendees .groupMember {
    min-height: 100%;
    min-width: 128px;
    padding-left: 8px;
    padding-right: 8px;
}

.embedpress-event .align--center {
    text-align: center;
}

.embedpress-event .card {
    background: #fff;
    background-clip: padding-box;
    background-size: cover;
    border: 1px solid rgba(46, 62, 72, .12);
    border-radius: 8px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: block;
    min-height: 100%;
    padding: 16px 16px 18px;
    position: relative;
    white-space: normal;
}

.embedpress-event .card--hasHoverShadow {
    transition: box-shadow .25s cubic-bezier(.4, 0, .2, 1), transform .25s cubic-bezier(.4, 0, .2, 1);
}

.embedpress-event .ep-event-group-link {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    background: #ffffff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid rgba(46, 62, 72, .12);
}

.embedpress-event .ep-event-group--name {
    padding-left: 20px;
    font-size: 14px;
    line-height: 1.45;
    margin: 0;
    width: 70%;
    word-break: break-word;
}

.embedpress-event .ep-event-group--image {
    -o-object-fit: cover;
    object-fit: cover;
    width: 56px;
    height: 56px;
    border-radius: 4px;
}

.embedpress-event .ep-event-time-location {
    background: #ffffff;
    border-radius: 8px;
    padding: 16px 20px 0 20px;
    border: 1px solid rgba(46, 62, 72, .12);
}

.embedpress-event .ep-event-time-location .ep-event-datetime,
.embedpress-event .ep-event-time-location .ep-event-location {
    padding-bottom: 20px;
}

.embedpress-event .ep-event-location .wrap--singleLine--truncate,
.embedpress-event .ep-event-time-location .ep-event-datetime {
    font-size: 15px;
    line-height: 1.5;
    color: #2e3e48;
    font-style: normal;
    margin: 0;
}

.embedpress-event .ep-event-location address {
    font-style: normal;
    margin: 0;
}

.embedpress-event .ep-event-location .venueDisplay-venue-address {
    font-style: normal;
    color: #757575;
    margin: 0;
}

.embedpress-event .ep-event-location p {
    line-height: 20px;
}

.ep-event--attendees .gridList-item {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: inline-block;
    font-size: 1rem;
    margin: 0;
    vertical-align: top;
    width: 50%;
}

.gridList-itemInner {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    height: 100%;
    padding: 0 16px 16px 0;
}

/*Youtube subscribe button*/
.embedpress-yt-subscribe {
    display: flex;
    align-items: center;
    align-content: start;
    text-align: center;
    gap: 1rem;
    max-width: 600px;
    width: 100%;
}

.embedpress-yt-subscribe .embedpress-yt-subscribe.dark {
    color: #ffffff;
}

.embedpress-yt-subscribe .embedpress-yt-sub-text {
    display: inline-block;
    margin: 0;
    padding: 0;
}

/* Meetup styling for new markup*/
.dewqijm {
    height: 100%;
    margin-right: 10px;
}

.dewqijm img[src^="image"] {
    display: none;
}

.dewqijm img[src^="http"] {
    height: 56px !important;
    width: 56px !important;
}

.dewqijm>div>div {
    display: none;
}

.embedpress-event .sticky button {
    display: none;
}

.embedpress-event .items-center {
    align-items: center;
}

/*Media query*/
@media only screen and (min-width: 530px) {
    .ep-event--attendees .gridList--autoHeight--has4>.gridList-item {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
    }
}

@media only screen and (min-width: 640px) {
    .embedpress-event .card {
        padding: 18px 18px 20px;
    }

    .ep-event--attendees .gridList--autoHeight--has4>.gridList-item {
        flex: 0 0 25%;
        max-width: 25%;
    }
}




/**
* NFT card frontend style
*/

.ose-opensea {
    height: 100% !important;
    width: calc(100% - 40px) !important;
    max-height: 100% !important;
}

.ose-github {
    overflow: auto;
}

.ep_nft_content_wrap.ep_nft__wrapper {
    display: grid;
}

.ep_nft_content_wrap.ep_nft__wrapper,
.ep_nft_content_wrap.ep_nft_list {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    grid-column-gap: 15px;
    grid-row-gap: 15px;
}

.ep_nft_content_wrap .ep_nft_item {
    padding-top: 15px;
    padding-right: 15px;
    padding-left: 15px;
    padding-bottom: 15px;
    background-color: #ffffff;
    border-radius: 10px;
    transition: background 0.5s, border 0.5s, border-radius 0.5s, box-shadow 0.5s;
    box-shadow: 0 4px 15px rgba(0, 0, 0, .09);
    overflow: hidden;
    padding: 15px;
    position: relative;
    transition: .3s ease-in-out;
}

.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-grid .ep_nft_item {
    display: flex;
    flex-direction: column;
}

.ep_nft_content_wrap.ep_nft_list .ep_nft_item {
    justify-content: flex-start;
    align-items: flex-start;
}

.ep_nft_content_wrap.ep_nft__wrapper.preset-3 .ep_nft_item .ep_nft_content {
    background-color: #edecf6e6;
}

.ep_nft_content_wrap .ep_nft_thumbnail {
    margin-top: 0px;
    margin-right: 0px;
    margin-left: 0px;
    margin-bottom: 15px;
    border-radius: 5px;
}

.ep_nft_content_wrap .ep_nft_thumbnail img {
    height: 340px;
    border-radius: 5px;
    width: 100%;
    object-fit: cover;
}

.ep_nft_content .ep_nft_title {
    color: #333333;
    font-size: 16px;
    margin-top: 0px;
    margin-right: 0px;
    margin-left: 0px;
    margin-bottom: 15px;
    font-weight: 600;
    word-break: break-all;
}

.ep_nft_content {
    text-align: left;
}

.ep_nft_content .ep_nft_price {
    color: #333333;
    font-size: 14px;
    margin-top: 0px;
    margin-right: 0px;
    margin-left: 0px;
    margin-bottom: 0px;
    display: flex;
    font-weight: 600;
}

.ep_nft_content .ep_nft_price:first-child {
    margin-bottom: 10px;
}

span.eb_nft_currency {
    max-width: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

span.eb_nft_currency svg {
    width: 100%;
    height: auto;
}

.ep_nft_content .ep_nft_price_wrapper {
    min-height: 20px;
}


.ep_nft_content .ep_nft_creator {
    color: #333333;
    font-size: 14px;
    margin-top: 0px;
    margin-right: 0px;
    margin-left: 0px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.ep_nft_content .ep_nft_creator a {
    color: #3080E2;
    font-size: 14px;
    text-decoration: none;
    word-break: break-all;
    text-decoration: none;
}

.ep_nft_content .ep_nft_creator img {
    height: 30px;
    width: 30px;
    border-radius: 50%;
}

.ep_nft_content .ep_nft_button button {
    margin-top: 0px;
    margin-right: 0px;
    margin-left: 0px;
    margin-bottom: 0px;
}

.ep_nft_content .ep_nft_button button a {
    background-color: #3080E2;
    color: #ffffff;
    font-size: 14px;
    padding-top: 15px;
    padding-right: 20px;
    padding-left: 20px;
    padding-bottom: 15px;
    transition: border 0.5s, border-radius 0.5s, box-shadow 0.5s;
}

.ep_nft_content .ep_nft_button button:hover a {
    background-color: rgb(46, 142, 238);
    color: #ffffff;
}

.ep-nft-gallery-wrapper .ep_nft_content_wrap.ep_nft__wrapper.ep-preset-1 .ep_nft_item:hover .ep_nft_button {
    opacity: 1;
    transform: translate(0);
    visibility: visible;
}


.ep-nft-gallery-wrapper.ep-nft-gallery-r1a5mbx .ep_nft_button a.ep-details-btn:hover {
    background-color: rgb(46, 142, 238);
    color: #ffffff;
}

.ep-nft-gallery-wrapper .ep_nft_content_wrap.ep_nft__wrapper.ep-preset-1 .ep_nft_item:hover .ep_nft_button {
    opacity: 1;
    transform: translate(0);
    visibility: visible;
}

.ep-nft-gallery-wrapper .ep_nft_content_wrap.ep_nft__wrapper.ep-preset-1 .ep_nft_item .ep_nft_button {
    bottom: 0;
    left: 0;
    opacity: 0;
    position: absolute;
    transform: translateY(30px);
    visibility: hidden;
    width: 100%;
    transition: 0.3s;
}

.ep-nft-gallery-wrapper.ep-nft-gallery-r1a5mbx .ep-nft-single-item-wraper .ep_nft_button {
    margin-top: auto;
    display: flex;
    justify-content: space-between;
    gap: 15px;
}

.ep-nft-gallery-wrapper.ep-nft-gallery-r1a5mbx .ep_nft_button a {
    background-color: #3080E2;
    color: #ffffff;
    font-size: 14px;
    padding: 10px 20px;
    transition: border 0.5s, border-radius 0.5s, box-shadow 0.5s;
    display: block;
    text-align: center;
    font-weight: 500;
    text-decoration: none;
}

.ep-nft-gallery-wrapper.ep-nft-gallery-r1a5mbx .ep_nft_button span.ep-nft-rank {
    color: #a88484;
    border-color: #a88484;
}

/* mimmikcssStart */


/* NFT List item CSS */

.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-list .ep_nft_item {
    display: flex;
    gap: 15px;
    align-items: center;
    border-radius: 10px;
}

.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-list .ep_nft_item .ep_nft_thumbnail {
    width: 55%;
}

.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-list .ep_nft_item .ep_nft_content {
    width: calc(45% - 15px);
}

.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-list .ep_nft_item .ep_nft_content .ep_nft_price.ep_nft_last_sale {
    margin-bottom: 15px;
}

.ep_nft_content_wrap.ep_nft__wrapper.nft_items .ep_nft_item .ep_nft_thumbnail svg {
    border-radius: 10px;
}

.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-list .ep_nft_item .ep_nft_thumbnail img {
    height: 260px;
}

.ep-nft-single-item-wraper.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-list .ep_nft_item .ep_nft_button a {
    border-radius: 10px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-grid.ep-preset-2 .ep_nft_content .ep_nft_price:last-child {
    margin-bottom: 15px;
}

.alignleft .ose-opensea,
.alignright .ose-opensea,
.aligncenter .ose-opensea {
    max-width: calc(100% - 40px) !important;
}

.embedpress-gutenberg-wrapper.alignright, .embedpress-gutenberg-wrapper.alignleft {
    width: 100%;
}

.embedpress-gutenberg-wrapper.alignright .ep-embed-content-wraper>div {
    float: right;
}

.embedpress-gutenberg-wrapper.aligncenter.ep-content-share-enabled .ep-embed-content-wraper {
    justify-content: center;
}

.embedpress-gutenberg-wrapper.alignright.ep-content-share-enabled .ep-embed-content-wraper {
    justify-content: right;
}

.embedpress-gutenberg-wrapper.alignleft.ep-content-share-enabled .ep-embed-content-wraper {
    justify-content: left;
}

.embedpress-gutenberg-wrapper.aligncenter {
    text-align: center;
    clear: both;
}

.theme-twentytwentythree footer.wp-block-template-part {
    clear: both;
}

/* mimmikcssEnd */

@media all and (max-width: 1024px) {

    /* tabcssStart */
    .ep_nft_content_wrap.ep_nft__wrapper,
    .ep_nft_content_wrap.ep_nft_list {
        grid-template-columns: repeat(3, 1fr) !important;
    }

    .alignleft .ose-opensea,
    .alignright .ose-opensea,
    .aligncenter .ose-opensea {
        max-width: 100% !important;
    }

    /* tabcssEnd */

}

@media all and (max-width: 991px) {

    /* tabcssStart */
    .ose-opensea {
        min-width: 100% !important;
        max-width: calc(100% - 40px) !important;
    }



    .ep_nft_content_wrap.ep_nft__wrapper,
    .ep_nft_content_wrap.ep_nft_list {
        grid-template-columns: repeat(2, 1fr) !important;
    }

    /* tabcssEnd */

}

@media all and (max-width: 767px) {

    /* mobcssStart */
    .ep_nft_content_wrap.ep_nft__wrapper,
    .ep_nft_content_wrap.ep_nft_list {
        grid-template-columns: repeat(1, 1fr) !important;
    }

    /* mobcssEnd */

}


/* NFT Single item CSS */
.ep-nft-single-item-wraper.ep_nft_content_wrap .ep_nft_item {
    box-shadow: none;
}

.ep-nft-rank-wraper {
    margin-bottom: 16px;
    font-weight: 600;
    color: #333;
    font-size: 16px;
}

.ep-nft-rank-wraper.ep-empty-label span {
    margin-left: 0px;
}

span.ep-nft-rank {
    padding: 2px 8px;
    border-radius: 7px;
    margin-left: 10px;
    display: inline-block;
    font-size: 14px;
    font-weight: 600;
    background: transparent !important;
    border: 1px solid #ddd;
    color: #333;
}

.ep-nft-single-item-wraper.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-list .ep_nft_item .ep_nft_content {
    width: calc(55% - 15px);
}

.ep-nft-single-item-wraper.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-list .ep_nft_item .ep_nft_thumbnail {
    width: 45%;
    height: 100%;
}

.ep-nft-single-item-wraper.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-list .ep_nft_item .ep_nft_thumbnail img {
    height: 100%;
}

.ep-nft-single-item-wraper span.eb_nft_label {
    color: #333;
    /* margin-bottom: -10px; */
}

.ep-nft-single-item-wraper .ep_nft_content .ep_nft_price {
    display: flex;
    flex-direction: column;
    margin-right: 45px;
}

.ep-nft-single-item-wraper span.eb_nft_price {
    font-size: 30px;
    line-height: normal;
}

.ep-nft-single-item-wraper .ep_nft_content .ep_nft_creator img {
    height: 15px;
    width: 15px;
    border-radius: 50%;
}

.ep-nft-single-item-wraper .ep-usd-price {
    bottom: 0;
    font-size: 12px;
}

.ep-nft-single-item-wraper span.eb_nft_label {
    /* margin-bottom: -14px; */
    font-size: 15px;
}

.ep-nft-single-item-wraper .ep_nft_content .ep_nft_title {
    margin-bottom: 10px;
    font-size: 24px;
}

.ep-nft-single-item-wraper .CollectionLink--name svg {
    width: 20px;
    height: 20px;
    margin-left: 5px;
}

.ep-nft-single-item-wraper a.CollectionLink--link {
    font-size: 15px;
    font-weight: 500;
    text-decoration: none;
    margin-bottom: 15px;
    display: block;
    color: #3080E2;
}

.ep-nft-single-item-wraper sub.verified-icon {
    bottom: -5px;
    left: 4px;
}

/* toggle */
.ep-nft-single-item-wraper .ep-accordion {
    border: 1px solid #ddd;
    border-radius: 10px;
    margin-top: 15px;
    display: block;
}

.ep-nft-single-item-wraper .ep-toggle {
    display: none;
}

.ep-nft-single-item-wraper .ep-option {
    position: relative;
}

.ep-nft-single-item-wraper .ep-content {
    padding: 1em;
    border-top: 1px solid #ddd;
}

.ep-nft-single-item-wraper .ep-content {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    transform: translateZ(0);
    transition: all 0.2s;
}

.ep-nft-single-item-wraper .ep-title {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    transform: translateZ(0);
    transition: all 0.2s;
    padding: 1em;
    display: flex;
    color: #333;
    font-weight: bold;
    cursor: pointer;
    border-radius: 10px 10px 0 0;
    align-items: center;
}

.ep-nft-single-item-wraper label.ep-title svg {
    width: 20px;
    height: 20px;
    margin-right: 6px;
}

.ep-nft-single-item-wraper .ep-asset-detail-item {
    display: flex;
    justify-content: space-between;
    font-weight: 500;
}

.ep-nft-single-item-wraper .ep-asset-detail-item span {
    word-break: break-word;
    max-width: 75%;
}

.ep-nft-single-item-wraper .ep-content {
    display: block;
}

.ep-nft-single-item-wraper .ep-content p {
    margin: 0;
    padding: 0.5em 1em 1em;
    font-size: 0.9em;
    line-height: 1.5;
}

.ep-nft-single-item-wraper .ep-toggle:checked+.ep-title+.ep-content {
    display: none;
    background: transparent;
}



.ep-nft-single-item-wraper .ep_nft_content .ep_nft_price_wrapper {
    display: flex;
}

.ep-nft-single-item-wraper .ep-toggle+.ep-title:after {
    content: "";
    display: inline-block;
    position: absolute;
    width: 12px;
    height: 12px;
    background: transparent;
    text-indent: -9999px;
    border-top: 2px solid #bfbfbf;
    border-left: 2px solid #bfbfbf;
    transition: all 250ms ease-in-out;
    text-decoration: none;
    color: transparent;
    right: 15px;
    top: 50%;
    transform: rotate(45deg) translate(-20%, -5%);
}

.ep-nft-single-item-wraper .ep-toggle:checked+.ep-title:before {
    content: "";
    display: inline-block;
    position: absolute;
    width: 12px;
    height: 12px;
    background: transparent;
    text-indent: -9999px;
    border-top: 2px solid #bfbfbf;
    border-left: 2px solid #bfbfbf;
    transition: all 250ms ease-in-out;
    text-decoration: none;
    color: transparent;
    right: 15px;
    top: 50%;
    transform: rotate(225deg) translate(80%, 20%);
}

.ep-nft-single-item-wraper .ep-toggle:checked+.ep-title:after {
    display: none;
}

.ep-nft-single-item-wraper.ep_nft_content_wrap.ep_nft__wrapper.nft_items .ep_nft_item .ep_nft_button span.ep-nft-rank {
    pointer-events: none;
}

.ep-nft-single-item-wraper.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-nft-single-item-wraper.ep-grid .ep_nft_content {
    margin-bottom: 20px;
}

.elementor-widget-embedpres_elementor .ep-nft-gallery-wrapper .ep-loadmore-wrapper button, .embedpress-gutenberg-wrapper .ep-nft-gallery-wrapper .ep-loadmore-wrapper button {
    display: none;
}

@media screen and (max-width: 1024px) {

    .ep-nft-single-item-wraper.ep_nft_content_wrap.ep_nft__wrapper, .ep-nft-single-item-wraper.ep_nft_content_wrap.ep_nft_list {
        grid-template-columns: repeat(1, 1fr) !important;
    }

}

@media screen and (max-width: 991px) {

    .ep-nft-single-item-wraper.ep_nft_content_wrap.ep_nft__wrapper, .ep-nft-single-item-wraper.ep_nft_content_wrap.ep_nft_list {
        grid-template-columns: repeat(1, 1fr) !important;
    }

    .ep-nft-single-item-wraper.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-list .ep_nft_item {
        align-items: unset;
        flex-direction: column;
    }

    .ep-nft-single-item-wraper.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-list .ep_nft_item .ep_nft_thumbnail {
        width: 100%;
    }

    .ep-nft-single-item-wraper.ep_nft_content_wrap.ep_nft__wrapper.nft_items.ep-list .ep_nft_item .ep_nft_content {
        width: 100%;
    }
}

@media screen and (max-width: 537px) {
    .ep-nft-single-item-wraper sub.ep-usd-price {
        margin-bottom: 15px;
        display: block;
    }

    .ose-google-drive iframe {
        height: 250px !important;

    }

    .ose-nrk-radio iframe {
        height: 400px !important;
    }

}

.emebedpress-unit-percent iframe {
    width: 100% !important;
}


/* Locked content form */

.password-form-container {
    width: 350px;
    text-align: center;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0px 0px 10px #f4eded;
    background-color: #f9f9ff;
    max-width: 100%;
    margin: auto;
}

.password-form-container h2 {
    font-size: 22px;
    margin-bottom: 15px;
    font-family: system-ui;
}

.password-form-container p {
    font-size: 12px;
    font-family: sans-serif;
    line-height: 1.4em;
    margin-bottom: 15px;
}

form.password-form {
    margin-bottom: 2px;
}

.password-form-container input[type="password"] {
    padding: 10px;
    border-radius: 5px;
    width: 100%;
    margin-bottom: 12px;
    font-size: 20px;
    color: #6354a5;
    outline: none;
    border: 1px solid #ddd;
    outline: 0;
    padding-left: 50px;
    font-family: sans-serif;
    height: 42px;
}

.password-form-container input[type="password"]::placeholder {
    color: #e0d1d1;
    font-size: 14px;
}

.password-field {
    position: relative;
}

.password-field span {
    position: absolute;
    top: 0px;
    left: 0px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 42px;
    width: 40px;
    background: #DED7FC;
    border-radius: 4px;
}

.password-field svg {
    width: 22px;
}

.password-form-container input[type="submit"] {
    padding: 12px 20px;
    background-color: #6354a5;
    color: #fff;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    font-family: sans-serif;
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
    font-weight: initial;
    white-space: normal;
    word-break: break-all;
    min-height: 42px;
}

.password-form-container button:hover {
    background-color: #0062cc;
    box-shadow: 0px 0px 10px #007bff;
}

.password-form-container .error-message {
    color: #ff0000;
    margin-top: 20px;
    font-size: 12px;
    line-height: 1.4em;
}

.password-form-container .error-message.hidden {
    display: none;
}

p.need-access-message {
    margin-bottom: 2px;
    margin-top: 13px;
}

.wp-block-embed__wrapper {
    display: inline-block;
    width: 100%;
    max-width: 100% !important;
}

.wp-block-embed__wrapper.position-right-wraper, .wp-block-embed__wrapper.position-right-wraper {
    max-width: calc(100% - 40px) !important;
}

.ep-elementor-content {
    position: relative;
    display: inline-block;
    max-width: 100%;
}

.ep-elementor-content.source-opensea {
    display: block;
}

/* social share css */
.wp-block-embedpress-embedpress>div, .ep-gutenberg-content {
    position: relative;
    clear: both;
}

.gutenberg-pdf-wraper {
    position: relative;
    width: 100%;
}

.presentationModeEnabledIosDevice {
    position: fixed;
    left: 0;
    top: 0;
    border: 0;
    height: 100% !important;
    width: 100% !important;
    z-index: 999999;
    min-width: 100% !important;
    min-height: 100% !important;
}

.ep-embed-content-wraper {
    width: 100%;
}

.embedpress-gutenberg-wrapper.popup_button, .embedpress-elements-wrapper.popup_button {
    display: none !important;
}

.elementor-element-edit-mode .embedpress-elements-wrapper.popup_button {
    display: block !important;
}

.ep-content-share-enabled .ep-embed-content-wraper {
    display: flex !important;
}

.ep-content-share-enabled .ep-embed-content-wraper.hidden {
    display: none !important;
}

.ep-share-position-right .ep-embed-content-wraper {
    flex-direction: row;
}

.ep-content-share-enabled .ep-embed-content-wraper>div:first-child {
    width: 100%;
}

.ep-share-position-left .ep-embed-content-wraper {
    flex-direction: row-reverse;
}

.ep-share-position-bottom .ep-embed-content-wraper {
    flex-direction: column;
}

.ep-share-position-top .ep-embed-content-wraper {
    flex-direction: column-reverse;
}

.ep-fixed-width .gutenberg-pdf-wraper {
    display: inline-block;
}

.ep-percentage-width .embedpress-inner-iframe {
    width: 100%;
}

/* social share css */

.ep-social-share {
    display: flex;
    align-items: center;
}

.ep-social-share.share-position-right {
    right: -40px;
    top: 0;
    flex-direction: column;
}

.ep-social-share.share-position-left {
    left: 0px;
    top: 0;
    flex-direction: column;
}

.ep-social-share.share-position-bottom, .ep-social-share.share-position-top {
    justify-content: center;
}

.embedpress-gutenberg-wrapper .ep-social-share.share-position-left, .ep-elementor-content .ep-social-share.share-position-left {
    left: -40px;
}

.ep-social-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    margin: 0;
    color: #fff;
    text-decoration: none;
    font-size: 20px;
}

.ep-social-icon:hover {
    opacity: 0.8;
}

.ep-social-share svg {
    width: 20px;
    height: auto;
}

a.ep-social-icon.pinterest svg {
    height: 25px;
}

.ep-social-share .facebook {
    background-color: #3b5998;
}

.ep-social-share .facebook svg {
    width: 40px;
}

.ep-social-share .twitter {
    background-color: #000000;
}

a.ep-social-icon.twitter:focus-visible {
    box-shadow: none;
    border: none;
    outline: none;
}

.ep-social-share .pinterest {
    background-color: #bd081c;
}

.ep-social-share .linkedin {
    background-color: #0077b5;
}

.ep-social-share .instagram {
    background: linear-gradient(45deg, #f58529, #dd2a7b, #8134af, #515bd4);
}

.ep-social-share .reddit {
    background-color: #ff4500;
}

.ep-social-icon i {
    margin-right: 0;
}

.ep-content-locked .watermark {
    display: none !important;
}

.ep-content-protection-enabled .watermark {
    display: none !important;
}

.embedpress-pro-control.not-active {
    pointer-events: none;
}

.pro__alert__wrap .pro__alert__card h2 {
    font-size: 32px;
    font-weight: 450;
    color: #131f4d;
    margin-bottom: 15px;
}

.pro__alert__wrap .pro__alert__card p {
    font-size: 14px;
    font-weight: 400;
    color: #7c8db5;
    margin-top: 10px;
}

.pro__alert__wrap .pro__alert__card p a {
    text-decoration: underline;
    font-weight: 700;
    color: #131f4d;
}

.emebedpress-unit-percent, .ep-elementor-content.ep-percentage-width {
    width: 100%;
}

/* Documents viewer style */
[data-theme-mode='dark'] {
    --viewer-primary-color: #343434;
    --viewer-icons-hover-bgcolor: #453838;

}

[data-theme-mode='light'] {
    --viewer-primary-color: #f2f2f6;
    --viewer-icons-color: #343434;
    --viewer-icons-hover-bgcolor: #e5e1e9;
}

@media (prefers-color-scheme: dark) {
    :root {
        --viewer-primary-color: #343434;
        --viewer-icons-color: #f2f2f6;
        --viewer-icons-hover-bgcolor: #453838;

    }
}

@media (prefers-color-scheme: light) {
    :root {
        --viewer-primary-color: #f2f2f6;
        --viewer-icons-color: #343434;
        --viewer-icons-hover-bgcolor: #e5e1e9;

    }
}

.ep-file-download-option-masked::after, .ep-external-doc-icons {
    background: var(--viewer-primary-color);
}

.ep-external-doc-icons svg path {
    fill: var(--viewer-icons-color);
}

.ep-doc-draw-icon svg path {
    fill: var(--viewer-primary-color);
    stroke: var(--viewer-icons-color);
}

.ep-external-doc-icons svg:hover svg path {
    fill: var(--viewer-icons-color);
    stroke: var(--viewer-icons-color);
}

.ep-external-doc-icons svg:hover {
    background-color: var(--viewer-icons-hover-bgcolor);
}

.ep-file-download-option-masked {
    position: relative;
}

.ep-file-download-option-masked {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.ep-file-download-option-masked .overlay {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 15px);
    height: calc(100% - 45px);
    background-color: rgb(66 23 23 / 0%);
    pointer-events: unset;
    z-index: 1;
    user-select: none;
    max-width: 800px;
    margin: 0 auto;
}


.ep-file-download-option-masked.ep-file-xls .overlay {
    height: calc(100% - 78px);
}

.ep-file-download-option-masked iframe {
    width: 100%;
    height: 100%;
    pointer-events: auto;
}


.ep-file-download-option-masked.enabled-text-copy {
    pointer-events: all;
}

.embed-download-disabled {
    width: 200px;
    height: 22px;
    background: #444444;
    position: absolute;
    right: 2px;
    bottom: 8px;
    opacity: 0;
}

.cui-toolbar-button-right {
    display: none !important;
}

.ndfHFb-c4YZDc-Wrql6b {
    display: none;
}

.ep-external-doc-icons {
    position: absolute;
    display: flex;
    flex-direction: revert;
    background: var(--viewer-primary-color);
    border-radius: 6px;
    z-index: 2;
    bottom: -18px;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 0 10px;
}

.ep-external-doc-icons svg {
    width: 30px;
    height: 30px;
    cursor: pointer;
    transition: 0.3s;
    padding: 5px;
    border-radius: 4px;
}

.block-editor-block-list__block .ep-gutenberg-file-doc .ep-external-doc-icons svg {
    width: 22px;
    height: 22px;
}

.block-editor-block-list__block .ep-gutenberg-file-doc .ep-doc-download-icon svg, .block-editor-block-list__block .ep-gutenberg-file-doc .ep-doc-fullscreen-icon svg {
    width: 20px !important;
}

.ep-doc-minimize-icon svg, .ep-doc-fullscreen-icon svg {
    padding: 6px;
}

.ep-external-doc-icons svg:hover {
    border-radius: 4px;
}

.ep-doc-draw-icon.active svg {
    background: var(--viewer-icons-hover-bgcolor);
}

.ep-doc-download-icon, .ep-doc-print-icon, .ep-doc-fullscreen-icon, .ep-doc-popup-icon, .ep-doc-draw-icon, .ep-doc-minimize-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    /* background: var(--viewer-primary-color); */
}

.elementor .elementor-element.elementor-element .embedpress-document-embed .fullscreen-enabled iframe, .embedpress-document-embed .fullscreen-enabled iframe {
    width: 100% !important;
    height: 100% !important;
}

.ep-file-download-option-masked.ep-file-link.fullscreen-enabled iframe {
    margin-left: 22%;
}

.ep-file-download-option-masked::after {
    position: absolute;
    width: 100%;
    height: 30px;
    background: var(--viewer-primary-color);
    z-index: 1;
    bottom: 0;
    content: '';
    left: 0;
}

.ep-file-download-option-masked.ep-file-docx::after {
    bottom: 0;
}

.ep-file-download-option-masked.ep-file-docx.ep-powered-by-enabled::after {
    bottom: 0px;
    background: white;
    height: 22px;
}

.ep-file-download-option-masked.ep-file-docx.ep-powered-by-enabled.fullscreen-enabled::after {
    bottom: 0;
}


canvas.ep-doc-canvas {
    position: absolute;
    z-index: 1;
    left: 0;
    bottom: 0;
    right: 0;
    top: 0;
    clear: both;
    margin: auto;
    display: none;
}

/* .ep-file-download-option-masked::after, .ep-file-download-option-masked iframe {
    pointer-events: none;
    user-select: none;
    opacity: 0;
  } */

/* custom player css */
:root {
    /* --plyr-color-main: #3700ff85; */
    --plyr-control-icon-size: 15px;
    --plyr-range-track-height: 3px;
    --plyr-range-thumb-height: 10px;

}

.plyr--audio .plyr--full-ui input[type=range] {
    color: red !important;
}

.ep-embed-content-wraper input[type=range]::-webkit-slider-runnable-track {
    box-shadow: none;
}

.ep-embed-content-wraper input[type=range]::-moz-range-track {
    box-shadow: none;
}

a.plyr__controls__item.plyr__control {
    border-radius: inherit;
    color: inherit !important;
}

.plyr-initialized button:focus {
    background-color: var(--plyr-color-main);
}

.custom-player-preset-1 .plyr__controls {
    background: var(--plyr-color-main) !important;
    padding: 0 !important;
}

.custom-player-preset-1 button.plyr__control {
    border-radius: 0;
}

.custom-player-preset-1 a.plyr__controls__item.plyr__control {
    border-radius: inherit;
}

.custom-player-preset-1 button.plyr__control.plyr__control--overlaid[data-plyr="play"], .custom-player-preset-2 button.plyr__control.plyr__control--overlaid[data-plyr="play"] {
    width: 100px !important;
    text-align: center;
    display: flex;
    align-items: center !important;
    justify-content: center;
    height: 60px;
}

.custom-player-preset-1 button.plyr__control.plyr__control--overlaid[data-plyr="play"] svg, .custom-player-preset-2 button.plyr__control.plyr__control--overlaid[data-plyr="play"] svg {
    width: 22px;
    height: 22px;
}

.custom-player-preset-1 .plyr__control:focus {
    border: none;
}

.custom-player-preset-2 a.plyr__controls__item.plyr__control, .custom-player-preset-2 button.plyr__control.plyr__control--overlaid[data-plyr="play"], .preset-default a.plyr__controls__item.plyr__control {
    border-radius: 4px;
}

.custom-player-preset-3 .plyr__controls, .custom-player-preset-4 .plyr__controls {
    background: var(--plyr-color-main) !important;
    padding: 0 !important;
}


.custom-player-preset-4 button.plyr__control.plyr__control--overlaid {
    display: none !important;
}

/* Overite plyr css  */
figure .plyr--full-ui .plyr__video-embed>.plyr__video-embed__container {
    padding-bottom: inherit !important;
}

.plyr {
    background-color: black;
}

.pip-mode {
    position: fixed !important;
    width: 320px !important;
    height: 180px !important;
    top: calc(100% - 200px) !important;
    left: calc(100% - 340px) !important;
    z-index: 9999;
    border-radius: 15px !important;

}

.plyr [data-plyr="pip"] {
    display: block !important;
}

.pip-mode .plyr__video-wrapper {
    pointer-events: none;

}

.pip-mode .pip-play {
    display: none;
}

.pip-mode .pip-play, .pip-mode .pip-pause {
    opacity: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #0000004a;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50px;
    cursor: pointer;
}

.pip-mode .pip-close {
    opacity: 0;
    position: absolute;
    right: 10px;
    top: 10px;
    background: #0000004a;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50px;
    cursor: pointer;
}

.pip-mode:hover .pip-play, .pip-mode:hover .pip-close, .pip-mode:hover .pip-pause {
    opacity: 1 !important;
}

.pip-mode .overlay-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.pip-mode iframe {
    pointer-events: none !important;
}

[data-playerid] {
    opacity: 0;
}

[data-playerid].audio.plyr-initialized {
    opacity: 1;
}

.plyr--paused.plyr__poster-enabled .plyr__poster {
    display: block !important;
    opacity: 1;
    background-size: cover;
}

@media only screen and (max-width: 767px) {
    .plyr__controls [data-plyr="restart"], .plyr__controls [data-plyr="rewind"], .plyr__controls [data-plyr="fast-forward"], .plyr__controls [data-plyr="pip"] {
        display: none !important;
    }

    .plyr__video-wrapper iframe {
        max-height: 100% !important;
    }

    .plyr__controls .plyr__volume input[type=range] {
        max-width: 40px;
        min-width: 40px;
    }
}

/* Instagram profile info design  */
/* Base styles for the profile header */
.profile-header {
    display: flex;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #ddd;
    justify-content: center;
    gap: 30px;
    padding-bottom: 30px;
}

/* Styling for the container holding the tabs */
.posts-tab-options {
    text-align: center;
    margin: 0;
}

/* Styling for the tabs */
.posts-tab-options .tabs {
    list-style: none;
    padding: 0;
    margin: 0;
    display: inline-flex;
    border-radius: 5px;
    gap: 25px;
    padding: 0;
}

.posts-tab-options .tabs svg {
    width: 15px;
    height: 15px;
}

li[data-media-type="ALL"] svg {
    width: 12px;
    height: 12px;
}

.posts-tab-options .tabs li {
    margin-right: 15px;
    cursor: pointer;
    padding: 10px 0;
    text-transform: uppercase;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.posts-tab-options .tabs li:last-child {
    margin-right: 0;
}

/* Styling for the active tab */
.posts-tab-options .tabs li.active {
    /* border-top: 1px solid rgb(115,115,115); */
    position: relative;
    color: rgb(115, 115, 115);

}

.posts-tab-options .tabs li svg {
    border-radius: 2px;
}

.posts-tab-options .tabs li.active svg {
    color: rgb(115, 115, 115);
}

.posts-tab-options .tabs li:hover, .posts-tab-options .tabs li:hover svg, .posts-tab-options .tabs li:hover .fill-color {
    color: rgb(115, 115, 115);
}

.posts-tab-options .tabs li:hover .fill-color, .posts-tab-options .tabs li.active .fill-color {
    fill: rgb(115, 115, 115) !important;
}

.posts-tab-options .tabs li.active::after {
    content: '';
    position: absolute;
    top: -1px;
    height: 1px;
    width: 100%;
    background-color: rgb(115, 115, 115);
    left: 0;
    right: 0;
}

/* Styles for the profile image and change photo button */
.profile-image {
    margin-right: 16px;
}

.posts-count {
    margin-right: 15px;
}

.profile-image img {
    width: 120px;
    height: 120px !important;
    object-fit: cover;
    border-radius: 50% !important;
    border: 3px solid #037bff;
}

.change-photo-button {
    background-color: transparent;
    border: none;
    cursor: pointer;
}

.change-photo-image {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
}

/* Styles for the profile username and edit profile button */
.username-section {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.profile-link {
    text-decoration: none;
}

.username {
    font-size: 18px;
    font-weight: bold;
    margin-right: 12px;
}

.edit-profile-link {
    text-decoration: none;
    color: #3897f0;
    font-weight: bold;
}

/* Styles for the profile stats */
.profile-stats {
    display: flex;
    margin-bottom: 8px;
}

.stats-list {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
}

.stats-list li {
    margin-right: 24px;
    font-size: 14px;
}

/* Styles for the profile bio */
.bio-section {
    margin-bottom: 12px;
    text-align: left;
}

section.profile-details div:last-child {
    margin: 0;
}


.bio {
    font-size: 14px;
}

a.edit-profile-link {
    padding: 8px 15px;
    background: #efefef;
    text-decoration: none !important;
    border-radius: 7px;
    color: #222;
    font-size: 12px;
    font-weight: 500;
}

span.count {
    font-weight: 600;
}

a.followers-link {
    text-decoration: none !important;
}


/* Hover effect for the edit profile button */

a.profile-link, a.profile-link h2 {
    font-size: 20px;
    text-decoration: none !important;
    margin: 0;
    margin-right: 15px;
}

/* Instagram feed layout design */
.embedpress-insta-container {
    overflow: hidden;
    position: relative;
}

.ose-instagram-feed {
    max-height: 100% !important;
    height: 100% !important;
}


.insta-grid .embedpress-insta-container .insta-gallery {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    /* Set 3 columns */
    gap: 15px;
    grid-auto-flow: row;
    /* Ensure items flow in rows */
}


.insta-carousel .embedpress-insta-container .insta-gallery {
    grid-auto-columns: calc(25% + 0px);
    gap: 0px;
    left: 0px;
}

.insta-masonry .embedpress-insta-container .insta-gallery {
    column-count: 3;
    gap: .5em;
    margin: auto;
    width: 100%;
    display: inherit;
}


.insta-grid .embedpress-insta-container .insta-gallery-item {
    position: relative;
    color: #fff;
    cursor: pointer;
    height: 350px;
}

.embedpress-insta-container .insta-gallery-item:hover {
    cursor: pointer;
}

.insta-masonry .embedpress-insta-container .insta-gallery .insta-gallery-item {
    height: auto;
    margin-bottom: .5em;
}


.insta-masonry .embedpress-insta-container .insta-gallery .insta-gallery-item video.insta-gallery-image {
    height: 550px;
    margin-bottom: -13px;
}

.embedpress-insta-container .insta-gallery-item .insta-gallery-item-info,
.embedpress-insta-container .insta-gallery-item .insta-gallery-item-info {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    transition: 0.3s;
    opacity: 0;
    visibility: hidden;
}

.insta-masonry .embedpress-insta-container .insta-gallery-item .insta-gallery-item-info {
    height: calc(100% + 4px);
}


.embedpress-insta-container .insta-gallery-item:hover .insta-gallery-item-info,
.embedpress-insta-container .insta-gallery-item:hover .insta-gallery-item-info {
    opacity: 1;
    visibility: visible;
}

.embedpress-insta-container .insta-gallery-item-info ul {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    margin: 0;
    height: 100%;
}

.embedpress-insta-container .insta-gallery-item-info li {
    display: flex;
    align-items: center;
    font-size: 1.7rem;
    font-weight: 600;
    gap: 5px;
}

.embedpress-insta-container .insta-gallery-item-likes {
    margin-right: 2.2rem;
}

.embedpress-insta-container .insta-gallery-item-type {
    position: absolute;
    right: 1rem;
    top: 1rem;
    text-shadow: 0.2rem 0.2rem 0.2rem rgba(0, 0, 0, .1);
}

.embedpress-insta-container .insta-gallery-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    min-height: 320px;
}

.insta-masonry .embedpress-insta-container .insta-gallery-image {
    margin-bottom: -13px;
}

.insta-gallery-item-info svg {
    width: 40px;
    height: 40px;
}

.insta-item-reaction-count {
    display: flex;
}

.insta-item-reaction-count .insta-gallery-item-likes, .insta-item-reaction-count .insta-gallery-item-comments {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    font-size: 20px;
    font-weight: bold;
    color: #fff;
}

.insta-gallery-item-info .insta-item-reaction-count svg {
    width: 25px;
    height: 25px;
}

.insta-gallery-item-type svg {
    width: 25px;
    height: 25px;
}

.insta-gallery-item-type svg.insta-video-icon {
    width: 22px;
    height: 22px;
}

.cg-carousel__btns.hidden {
    display: none;
}

.cg-carousel__btns {
    top: 50%;
    transform: translateY(-50%);
    position: absolute;
    width: 100%;
}

.ep-embed-content-wraper.insta-carousel .cg-carousel__btns {
    top: calc(50% - 16px);
}

.ep-embed-content-wraper.insta-carousel .insta-gallery-item-permalink {
    display: flex;
    align-content: center;
}

.cg-carousel__btns button {
    background: #262323;
    border: 0;
    border-radius: 50px;
    width: 35px;
    height: 35px;
    display: inline-grid;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    padding: 0;
}

button#js-carousel__prev-1 {
    position: absolute;
    left: 30px;
    /* top: 50%;
    transform: translateY(-50%); */
    z-index: 1;
    padding: 8px 15px;

}

button#js-carousel__next-1 {
    position: absolute;
    right: 30px;
    /* top: 50%;
    transform: translateY(-50%); */
    z-index: 1;
    padding: 8px 15px;

}

.embedpress-popup-block.embedpress-popup-img {
    position: relative;
    height: 100%;
    display: flex;
    background: #000;
}

.popup-carousel, .cg-carousel__track {
    height: 100%;
}

.popup-container .popup-carousel .cg-carousel__track {
    align-items: center;
    background-color: #000;
}

.popup-container .popup-carousel {
    background-color: #000;
}



button.js-carousel__prev-1 {
    position: absolute;
    left: 30px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;

}

button.js-carousel__next-1 {
    position: absolute;
    right: 30px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;

}

.cg-carousel__btn svg {
    width: 16px;
    height: 16px;
}

.load-more-button-container, .load-more-button-container button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.load-more-button-container button {
    border-style: solid;
    border-top-width: 0;
    border-right-width: 0;
    border-left-width: 0;
    border-bottom-width: 0;
    color: #ffffff;
    border-color: #037bff;
    background-color: #037bff;
    padding-top: 15px;
    padding-right: 30px;
    padding-bottom: 15px;
    padding-left: 30px;
    font-family: inherit;
    font-weight: inherit;
    line-height: 1em;
    cursor: pointer;
    border-radius: 5px;
    margin-top: 30px;
}


/* Popup design for instafeed */


.popup-container {
    display: flex;
    width: 100%;
}

/* Wrapper */
.popup-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.85);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 99999;
}

/* Popup Container */
.popup {
    border-radius: 10px;
    overflow: hidden;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
    max-height: 100%;
    max-width: 100%;
}

/* .popup>div {
    height: calc(100% - 100px);
} */

/* .popup-md-3.red {
    display: flex;
    align-items: center;
} */

.popup-container .popup-md-9.white {
    width: 60%;
    overflow: hidden;
}

.popup-md-3.red {
    width: 40%;
}

/* Image Block */
.embedpress-popup-img img {
    width: 100%;
    height: auto;
    object-fit: contain;
}

video.popup-media-image {
    height: auto;
    max-width: 100%;
    width: 100%;
    max-height: 100vh;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
}

img.popup-media-image {
    height: auto;
}

/* Header */
.embedpress-popup-header, .embedpress-hashtag-header {
    display: flex;
    align-items: center;
    padding: 15px 10px;
    justify-content: space-between;
    border-bottom: 1px solid #f4e9e9;
}

.hashtag-container {
    border-bottom: 1px solid #f4e9e9;
}

.embedpress-hashtag-header {
    border: none;
    max-width: 350px;
    margin: auto;
}

.embedpress-hashtag-username {
    font-weight: bold;
}

.embedpress-popup-header-img img, .embedpress-hashtag-header-img img {
    border-radius: 50% !important;
    margin-right: 10px;
    width: 30px;
    height: 30px !important;
    object-fit: cover;
    border: 2px solid #037bff !important;
}

.embedpress-popup-header-img a, .embedpress-hashtag-header-img a {
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none !important;
}

.embedpress-popup-username {
    color: #005293;
    font-weight: bold;
    font-size: 15px;
    text-decoration: none !important;
}

img.embedpress-hashtag-round {
    border-radius: 50px !important;
}

.insta-followbtn a {
    text-decoration: none !important;
    padding: 6px 15px;
    background: #007bff;
    border-radius: 5px;
    color: #fff !important;
    font-size: 14px;
    font-weight: normal;
}

/* Text */
.embedpress-popup-block.embedpress-popup-info {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex-grow: 1;
    height: 100%;
    background-color: #fff;
    /* max-height: calc(100% - 67px);
    margin-top: 33px; */
}

.embedpress-popup-text {
    font-size: 14px;
    font-weight: normal;
    overflow: hidden;
    letter-spacing: 0.04em;
    line-height: 1.8rem;
    padding: 10px;
    flex: auto;
    border-bottom: 1px solid #f4e9e9;
    text-align: left;
    max-height: calc(100vh - 235px);
    overflow: auto;
    word-break: break-all;
}

/* Stats */
.embedpress-popup-stats {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 10px;
}

.embedpress-popup-stats .embedpress-inline {
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: bold;
    color: #000;
    cursor: pointer;
    position: relative;
}

.embedpress-popup-stats .embedpress-inline a {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #000;
    text-decoration: none;
    font-weight: 400;
}

.embedpress-popup-stats .embedpress-inline a svg {
    width: 20px;
    height: 20px;
}

/* Share Buttons */

.embedpress-popup-share-buttons {
    padding: 10px;
    background-color: #fafafa;
    display: flex;
    align-items: center;
}

.embedpress-popup-share-buttons a {
    color: #007bff;
}

.embedpress-href.embedpress-popup-share {
    cursor: pointer;
    color: #ca379d;
    display: flex;
    align-items: center;
    gap: 6px;
}

.embedpress-popup-share-buttons {
    background: #f4f9ff;
    position: absolute;
    top: -50px;
    left: -55px;
    gap: 10px;
    border-radius: 5px;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
    visibility: hidden;
    opacity: 0;
    margin-bottom: -10px;
    transition: 0.3s;
}

.embedpress-popup-share-buttons.show {
    visibility: visible;
    opacity: 1;
    margin-bottom: 0;
}

.embedpress-popup-share-buttons::before {
    content: "";
    position: absolute;
    bottom: -15px;
    left: 50%;
    margin-left: -8px;
    border-width: 8px;
    border-style: solid;
    border-color: #f4f9ff transparent transparent transparent;
}

.embedpress-popup-share-buttons::before {
    content: "";
    position: absolute;
    bottom: -15px;
    left: 50%;
    margin-left: -8px;
    border-width: 8px;
    border-style: solid;
    border-color: #f4f9ff transparent transparent transparent;
    z-index: 1 !important;
}

.embedpress-popup-share-buttons::after {
    content: "";
    position: absolute;
    bottom: -16.1px;
    left: 50%;
    margin-left: -8px;
    border-width: 8px;
    border-style: solid;
    border-color: #e7eaef transparent transparent transparent;
}

.embedpress-popup-share-buttons span {
    color: #fff;
    font-size: 18px;
    display: flex;
    align-items: center;
}

.embedpress-popup-share-buttons span svg {
    width: 16px !important;
    height: 16px !important;
}

.embedpress-popup-share-buttons span.dashicons.dashicons-linkedin {
    color: #0077b5;
}

.embedpress-popup-share-buttons span.dashicons.dashicons-facebook {
    color: #1877F2;
}

.embedpress-popup-share-buttons span.dashicons.dashicons-pinterest {
    color: #E60023;
}

span.tag-wrapper a {
    font-weight: bold;
    text-decoration: none !important;
}

.embedpress-href.embedpress-popup-share svg {
    width: 20px;
    height: 20px;
}

.embedpress-popup-instagram-buttons svg {
    width: 18px !important;
    height: 20px !important;
}

.embedpress-popup-stats .embedpress-inline {
    transition: 0.3s;
}

.embedpress-popup-stats .embedpress-inline:hover svg, .embedpress-popup-stats .embedpress-inline a:hover, .embedpress-popup-stats .embedpress-inline:hover, .embedpress-href:hover svg .st0 {
    color: rgb(115, 115, 115) !important;
    /* stroke: rgb(115, 115, 115); */
}

.embedpress-inline.embedpress-popup-instagram-buttons a:hover svg path {
    stroke: rgb(115, 115, 115);
}

.embedpress-inline.popup-like-button a:hover svg {
    color: rgb(115, 115, 115);
    fill: rgb(115, 115, 115);
}

.embedpress-href.embedpress-popup-share:hover svg {
    fill: rgb(115, 115, 115);
}

/* Date */
.date-post {
    padding: 10px;
    font-size: 12px;
    color: #8e8e8e;
}

div.popup-close {
    position: absolute;
    top: 50px;
    right: 50px;
    color: white;
    z-index: 15566555655;
    cursor: pointer;
    padding: 15px;
    background: #643535;
    border-radius: 50px;
    height: 10px;
    width: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-transform: uppercase;
    cursor: pointer;
}

.load-spin {
    width: 100px;
    height: 100px;
    background-color: #f00;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}


.loader {
    margin: auto;
    border: 5px solid #EAF0F6;
    border-radius: 50%;
    border-top: 5px solid #FF7A59;
    width: 30px;
    height: 30px;
    animation: spinner 4s linear infinite;
}

.sponsored-youtube-video {
    height: 100%;
}

@keyframes spinner {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@media screen and (max-width: 1200px) {

    .popup-container .popup-md-9.white {
        width: 50%;
    }

    .popup-container .popup-md-3 {
        width: 50%;
    }

}

@media screen and (max-width: 991px) {
    .popup-container {
        flex-direction: column;
    }

    .popup-container .popup-md-9.white {
        width: 100%;
    }

    .popup-container .popup-md-3 {
        width: 100%;
    }

    .embedpress-popup-text {
        max-height: 300px;
    }

}

@media screen and (max-width: 768px) {
    .insta-grid .embedpress-insta-container .insta-gallery {
        grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
    }

    .embedpress-popup-text {
        max-height: 250px;
    }
}

@media screen and (max-width: 420px) {
    .insta-grid .embedpress-insta-container .insta-gallery {
        grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
    }

    .profile-header {
        flex-direction: column;
    }

    .embedpress-popup-text {
        max-height: 200px;
    }

    .insta-followbtn {
        display: none;
    }
}

/* youtube channel css */

.ep-player-wrap .hide {
    display: none;
}

.ep-gdrp-content {
    background: #222;
    padding: 50px 30px;
    color: #fff;
}

.ep-gdrp-content a {
    color: #fff;
}

.ep-youtube__content__pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 30px;
    gap: 10px;
}

.ep-loader-wrap {
    margin-top: 30px;
    display: flex;
    justify-content: center;
}

.ep-youtube__content__pagination .ep-prev,
.ep-youtube__content__pagination .ep-next {
    cursor: pointer;
    border: 1px solid rgba(0, 0, 0, .1);
    border-radius: 30px;
    padding: 0 20px;
    height: 40px;
    transition: .3s;
    display: flex;
    align-items: center;
}

.ep-youtube__content__pagination .ep-prev:hover,
.ep-youtube__content__pagination .ep-next:hover {
    background-color: #5B4E96;
    color: #fff;
}

.ep-youtube__content__pagination .ep-page-numbers {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.ep-youtube__content__pagination .ep-page-numbers>span {
    border: 1px solid rgba(0, 0, 0, .1);
    border-radius: 30px;
    display: inline-block;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.active__current_page {
    background: #5B4E96;
    color: #fff;
}

.ep-youtube__content__block .youtube__content__body .content__wrap:not(.youtube-carousel) {
    margin-top: 30px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}


[data-source-id] .layout-list .ep-youtube__content__block .youtube__content__body .content__wrap {
    grid-template-columns: repeat(auto-fit, minmax(calc(100% - 30px), 1fr)) !important;
}

[data-youtube-channel-carousel].ep-youtube__content__block {
    position: relative
}

.ep-youtube__content__block .item {
    cursor: pointer;
    white-space: initial;
}

.ep-youtube__content__block .youtube-carousel .item {
    margin: 10px;
}

.ep-youtube__content__block .item:hover .thumb .play-icon {
    opacity: 1;
    top: 50%;
}

.ep-youtube__content__block .item:hover .thumb:after {
    opacity: .4;
    z-index: 0;
}

.ep-youtube__content__block .thumb {
    padding-top: 56.25%;
    margin-bottom: 5px;
    position: relative;
    background-color: #222;
    background-size: contain !important;
    border-radius: 12px;
    overflow: hidden;
}

figure .ep-youtube__content__block .thumb {
    background-color: #222 !important;
}

.ep-youtube__content__block .thumb:after {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    content: '';
    background: #000;
    opacity: 0;
    transition: opacity .3s ease;
}

.ep-youtube__content__block .thumb:before {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    content: '';
    background: #222;
    z-index: -1;
}

.ep-youtube__content__block .thumb img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.ep-youtube__content__block .thumb .play-icon {
    width: 50px;
    height: auto;
    position: absolute;
    top: 40%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: all .3s ease;
    z-index: 2;
}

.ep-youtube__content__block .thumb .play-icon img {
    width: 100;
}

.ep-youtube__content__block .body p {
    margin-bottom: 0;
    font-size: 15px;
    text-align: left;
    line-height: 1.5;
    font-weight: 400;
}

.ep-youtube__content__block.loading .ep-youtube__content__pagination {
    display: none;
}

.ep-youtube__content__block .ep-loader {
    display: none;
}

.ep-youtube__content__block.loading .ep-loader {
    display: block;
}

.ep-loader img {
    width: 20px;
}

.is_mobile_device {
    display: none !important;
}


.is_mobile_devic.ep-page-numbers {
    gap: 5px;
}

@media only screen and (max-width: 480px) {
    .is_desktop_device {
        display: none !important;
    }

    .ep-youtube__content__pagination .ep-page-numbers>span {
        width: 35px;
        height: 35px;
    }

    .ep-youtube__content__pagination .ep-prev, .ep-youtube__content__pagination .ep-next {
        height: 35px;
    }

    .is_mobile_device {
        display: flex !important;
        ;
    }

    .ep-youtube__content__pagination .ep-page-numbers {
        gap: 5px;
    }
}

/* Youtube Advance layout style */
.embedded-youtube-channel .ose-youtube {
    height: 100% !important;
    max-height: 100% !important;
}

.ep-embed-content-wraper.embedded-youtube-channel .ose-youtube, .ep-youtube-channel .ose-youtube {
    height: 100% !important;
    max-height: 100% !important;
}

.ose-youtube .channel-header {
    display: flex;
    align-items: center;
    padding: 20px;
    border-radius: 10px;
}

.ose-youtube .profile-picture {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    margin-right: 20px;
}

.ose-youtube .channel-info {
    flex-grow: 1;
    text-align: left;
}

.ose-youtube .info-description {
    margin-bottom: 20px;
}



.ose-youtube .channel-name {
    font-size: 24px;
    margin: 0;
}

.ose-youtube .channel-details {
    margin: 5px 0;
}

.ose-youtube .more-info {
    text-decoration: none;
}

.ose-youtube .subscribe-button {
    background-color: red;
    border: none;
    padding: 10px 20px 10px 12px;
    border-radius: 30px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    /* display: inline; */
    text-decoration: none !important;
    color: white;
    font-weight: 600;
}

.ose-youtube .subscribe-button svg {
    height: 20px;
    width: 20px;
    margin-right: 5px;
}

.ose-youtube .subscribe-button:hover {
    background-color: #ff6868;
}

#videoPopup.video-popup {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.95);
}

.video-popup-content {
    position: absolute;
    left: 50%;
    top: calc(50% + 30px);
    transform: translate(-50%, -50%);
    max-width: 90%;
    max-height: 90%;
    text-align: center;
}

.video-popup-inner-content {
    width: 100%;
    height: 100%;
    max-height: 90vh;
    overflow: auto !important;
    border: 1px solid #333;
    border-radius: 8px;
    background: #000;
    box-sizing: border-box;
    padding: 20px;

}

.video-popup-content iframe {
    width: 700px !important;
    height: 420px !important;
    max-width: 100%;
    max-height: 100%;
}

#videoPopup.video-popup .close {
    position: absolute;
    top: -25px;
    right: -30px;
    color: #fff;
    font-size: 35px;
}

#videoPopup.video-popup .close:hover,
#videoPopup.video-popup .close:focus {
    color: #999;
    text-decoration: none;
    cursor: pointer;
}

#videoPopup.video-popup .popup-controls {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    pointer-events: none;
    /* Allow clicks to pass through to iframe */
}

#videoPopup.video-popup .nav-icon {
    pointer-events: auto;
    /* Enable click events */
    color: white;
    font-size: 50px;
    cursor: pointer;
    user-select: none;
}

#videoPopup.video-popup .prev-icon {
    position: absolute;
    left: -60%;
    top: 50%;
    transform: translate(0, -50%);
    padding: 10px;
}

#videoPopup.video-popup .next-icon {
    position: absolute;
    right: -60%;
    top: 50%;
    transform: translate(0, -50%);
    padding: 10px;
}

#videoDescription {
    color: #fff;
    font-size: 14px;
}


.youtube-video-description {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0 20px;
    bottom: no;
    max-width: 700px;
    text-align: left;
}

.youtube-video-header h1 {
    font-size: 20px;
    margin: 0 0 10px 0;
    color: #fff;
}

.youtube-video-meta {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
}

.youtube-video-meta span {
    margin-right: 10px;
}

.youtube-video-body {
    max-height: 200px;
    overflow: auto;
}

.youtube-video-body p {
    font-size: 15px;
    margin: 0 0 20px 0;
    color: #ddd;
}

.youtube-video-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 5px;
}

.youtube-video-link {
    font-size: 16px;
    color: #0073aa;
    text-decoration: none;
}

.youtube-video-link:hover {
    text-decoration: underline;
}

.youtube-video-stats {
    font-size: 14px;
    color: #666;
    display: flex;
    align-items: center;
    gap: 18px;
}

.youtube-video-stats svg {
    height: 12px;
    width: 14px;
}

.youtube-video-stats span {
    margin-right: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #dad2d2;
}

.youtube-video-stats i {
    margin-right: 5px;
}

/* grid item description */

.youtube-body-content .description-container {
    display: flex;
    /* background-color: #ffffff; */
    border-radius: 4px;
    max-width: 600px;
    text-align: left;
    margin-top: 10px;
}

.youtube-body-content.body {
    width: 100%;
}

.youtube-body-content .thumbnail {
    width: 30px;
    height: 30px;
    border-radius: 4px;
    margin-right: 10px;
}

.youtube-body-content .thumbnail img {
    border-radius: 50%;
    margin-right: 10px;
    width: 30px;
    height: 30px;
}

.youtube-body-content .details {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: calc(100% - 30px);
}

.youtube-body-content .title {
    font-size: 14px;
    font-weight: 500;
    color: #000000;
    margin-bottom: 4px;
    line-height: 1.4;
}

.youtube-body-content .channel,
.youtube-body-content .views,
.youtube-body-content .time {
    font-size: 14px;
    color: #606060;
}

.youtube-body-content .channel {
    margin-bottom: 4px;
}

/* Youtube List layout design */

.ep-player-wrap.layout-gallery .ep-first-video iframe {
    border-radius: 15px;
}

.ep-player-wrap.layout-list .ep-youtube__content__block .item {
    display: flex;
    width: 100%;
    gap: 20px;
}

.ep-player-wrap.layout-list .ep-youtube__content__block .thumb {
    padding-top: 20.25%;
    min-width: 250px;
    max-width: 300px;
}

/* Youtube Carousel Css */
[data-youtube-channel-carousel] .youtube-carousel-container {
    position: relative;
    width: 100%;
    overflow: hidden;
}

[data-youtube-channel-carousel] .youtube-carousel {
    display: flex;
    transition: transform 0.5s ease-in-out;
}

[data-youtube-channel-carousel] .youtube-carousel .item {
    min-width: calc(33.3333% - 20px);
    /* Adjust this value to show more or fewer items */
    box-sizing: border-box;
}



[data-youtube-channel-carousel] button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    color: white;
    border: none;
    cursor: pointer;
    padding: 10px;
    box-shadow: none;
    background-color: #000000ba;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    border-radius: 50%;
    justify-content: center;
}

[data-youtube-channel-carousel] button.preview {
    left: -100px;

}

.carousel-controls button.next {
    right: -100px;
}


/* full calender style overide */

.fc-list-table .fc-event, .fc-list-table .fc-event:hover {
    color: inherit !important;
    text-decoration: none !important;
}

.fc-list-table .fc-event, .fc-list-table .fc-event-dot {
    background-color: transparent !important;
}

.fc table.fc-list-table {
    table-layout: inherit !important;
}

.fc-list-table .fc-event {
    display: table-row !important;
}

@media screen and (max-width: 1400px) {
    .ose-youtube .video-popup-content iframe {
        height: 305px !important;
    }

    .ose-youtube .prev-icon {
        left: -50%
    }

    .ose-youtube .next-icon {
        right: -50%
    }
}

@media screen and (max-width: 1200px) {
    .ose-youtube .video-popup-content iframe {
        height: 305px !important;
    }

    .ose-youtube .prev-icon {
        left: -40%
    }

    .ose-youtube .next-icon {
        right: -40%
    }
}

@media screen and (max-width: 992px) {
    .ose-youtube .video-popup-content iframe {
        height: 305px !important;
    }

    .ose-youtube .prev-icon {
        left: -40%
    }

    .ose-youtube .next-icon {
        right: -40%
    }
}


@media screen and (max-width: 576px) {
    .ose-youtube .prev-icon {
        left: -26%
    }

    .ose-youtube .next-icon {
        right: -26%
    }
}

@media screen and (max-width: 580px) {
    .ose-youtube .prev-icon {
        left: -18%;
    }

    .ose-youtube .next-icon {
        right: -18%;
    }
}



.youtube-video-body {
    display: none;
}


.video-popup-content {
    width: 100%;
    max-width: 720px;
    margin: auto;
}

@media (max-width: 1024px) {
    .youtube-carousel .item {
        min-width: calc(50% - 20px) !important;
        /* Show 2 items */
    }
}

@media (max-width: 768px) {
    .youtube-carousel .item {
        min-width: calc(100% - 20px) !important;
        /* Show 1 item */
    }

    [data-youtube-channel-carousel] button.preview {
        left: 0 !important;
    }

    [data-youtube-channel-carousel] button.next {
        right: 0;
    }

    .video-popup-content {
        width: calc(100% - 40px);
        margin: auto;
    }

    .video-popup-content iframe {
        max-height: 300px;
    }

    #videoPopup.video-popup .close {
        top: -25px;
        transform: translate(0%, -50%);
        right: unset;
    }


}

@media (max-width: 580px) {
    .ep-first-video {
        margin-bottom: 20px;
    }

    .video-popup-content iframe {
        max-height: 220px;
    }

    .ep-player-wrap .ep-youtube__content__block .item {
        gap: 15px;
    }

    .ep-embed-content-wraper .ep-youtube__content__block .youtube__content__body .content__wrap {
        gap: 30px !important;
    }

    .ose-youtube .profile-picture {
        width: 80px;
        height: 80px;
    }

    .ose-youtube .channel-header {
        gap: 10px;
        align-items: start;
        padding: 0;

    }

    .description-container {
        margin-top: 0px;
    }

    .description-container p.description {
        display: none;
    }

    .ep-player-wrap.layout-list .ep-youtube__content__block .item {
        flex-direction: column;
    }

    .ep-player-wrap.layout-list .ep-youtube__content__block .thumb {
        min-width: 100%;
        min-height: 200px;
    }

}

.ep-google-photos-gallery-grid .ose-google-photos,
.ep-google-photos-gallery-masonary .ose-google-photos,
.ep-google-photos-gallery-justify .ose-google-photos {
    height: 100% !important;
    max-height: 100% !important;
}


/* Leons style */
/* Basic styling */
.photos-gallery-grid, .photos-gallery-masonry, .photos-gallery-justify {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 3px;
}

.photos-gallery-grid .photo-item, .photos-gallery-masonary .photo-item {
    width: 100%;
    height: 200px;
    background-color: #f1efef;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    text-align: center;
}


/* masonary */

.photos-gallery-masonary {
    columns: 250px;
    gap: 3px;
    position: relative;
    width: 100%;
}

.photos-gallery-masonary .photo-item {
    width: 100%;
    height: auto;
    object-fit: cover;
    display: block;
    margin-bottom: 3px;
    cursor: pointer;
}

.photos-gallery-masonary .photo-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

/* grid */


.photos-gallery-grid {
    display: grid;
    gap: 3px;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    position: relative;
}

.photos-gallery-grid .photo-item {
    border-radius: 10px;
    text-align: center;
    height: 280px;
    cursor: pointer;
}

.photos-gallery-grid .photo-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

/* justify */
/* .photos-gallery-justify {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 3px;
}

.photos-gallery-justify .photo-item {
    flex: 1 0 auto;
    width: auto;
    height: 250px;

}

.photos-gallery-justify .photo-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
} */


.photos-gallery-justify {
    display: flex;
    flex-wrap: wrap;
}

.photos-gallery-justify .photo-item {
    cursor: pointer;
    height: auto;
}

.photos-gallery-justify .photo-item:hover {
    opacity: 0.9;
}

.photos-gallery-justify .photo-item img {
    user-select: none;
    width: 100%;
    vertical-align: middle;
}

.photos-gallery-justify::after {
    content: "";
    flex-grow: 99999;
    min-width: calc(100vw / 4);
}

@media (max-width: 460px) {
    .photos-gallery-justify {
        flex-direction: column;
    }

    .photos-gallery-justify .photo-item {
        width: 100% !important;
    }
}



/* Popup styles */
.popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    transition: 0.5s ease;

}

.popup-overlay .popup {
    position: relative;
    max-width: 100%;
    max-height: 100%;
    width: 100%;
    height: 100%;
    background-color: transparent;
    display: flex !important;
    justify-content: center;
    align-items: center;
    color: black;
}

.popup-overlay .close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    color: white;
    font-size: 30px;
    cursor: pointer;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #0a0a0a;
    border-radius: 50%;
}

.popup-overlay .prev-btn,
.popup-overlay .next-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 30px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    padding: 10px;
    cursor: pointer;
    z-index: 10;
    opacity: 0;
    visibility: hidden;
    transition: 0.2s;
    display: flex;
}

.popup-overlay:hover .prev-btn, .popup-overlay:hover .next-btn {
    opacity: 1;
    visibility: visible;

}

.popup-overlay .prev-btn {
    left: 10px;
}

.popup-overlay .next-btn {
    right: 10px;
}

.popup-overlay img#popup-image {
    width: 100%;
    height: auto;
    max-height: 100vh;
}

.popup-overlay .prev-btn:hover,
.popup-overlay .next-btn:hover {
    background-color: rgba(0, 0, 0, 0.8);
}


@media (max-width: 1024px) {

    .photos-gallery-grid .photo-item {
        width: 100%;
        height: 150px !important;
    }


    /* masonary */

    .photos-gallery-masonary {
        columns: 200px !important;
    }

    /* grid */


    .photos-gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    }

}


@media (max-width: 767px) {

    .popup-overlay .prev-btn,
    .popup-overlay .next-btn {
        font-size: 20px;
        padding: 5px;
    }
}