@font-face {
    font-family: 'el-icon';
    src:  url('../fonts/el-icon.eot?u9p5gz');
    src:  url('../fonts/el-icon.eot?u9p5gz#iefix') format('embedded-opentype'),
    url('../fonts/el-icon.ttf?u9p5gz') format('truetype'),
    url('../fonts/el-icon.woff?u9p5gz') format('woff'),
    url('../fonts/el-icon.svg?u9p5gz#el-icon') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: block;
}

[class^="icon-"]:before, [class*=" icon-"]:before {
    font-family: "el-icon";
    font-style: normal;
    font-weight: normal;
    speak: never;

    display: inline-block;
    text-decoration: inherit;
    width: 1em;
    margin-right: .2em;
    text-align: center;
    /* opacity: .8; */

    /* For safety - reset parent styles, that can break glyph codes*/
    font-variant: normal;
    text-transform: none;

    /* fix buttons height, for twitter bootstrap */
    line-height: 1em;

    /* Animation center compensation - margins should be symmetric */
    /* remove if not needed */
    margin-left: .2em;

    /* you can be more comfortable with increased icons size */
    /* font-size: 120%; */

    /* Font smoothing. That was taken from TWBS */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    /* Uncomment for 3D effect */
    /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */
}

.icon-document:before { content: '\e800'; } /* '' */
.icon-embedpress:before { content: '\e801'; } /* '' */
.icon-pdf:before { content: '\e802'; } /* '' */
/*EmbedPress Pro Controls Style Starts*/
.elementor-control.embedpress-pro-control{
    position: relative;
    opacity: .9;
}
/*Second long class needed for yt channel link control, otherwise it wont get effected.*/
.elementor-control.embedpress-pro-control::before,
.elementor-control.elementor-control-yt_sub_channel.elementor-control-type-text.embedpress-pro-control.elementor-label-block.elementor-control-separator-default.elementor-control-dynamic::before,
.elementor-control.elementor-control-yt_lc_show.elementor-control-type-switcher.embedpress-pro-control.elementor-label-inline::before{
    content: '';
    position: absolute;
    width: 100%;
    height: 100%!important;
    display: block;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 99;
    background: transparent!important;
}


.elementor-control.elementor-control-embedpress_pro_section.elementor-control-type-section.elementor-label-inline.elementor-control-separator-none .elementor-panel-heading-title {
    color: #ff5544;
}

.elementor-control-pagesize .elementor-label-inline>.elementor-control-content>.elementor-control-field>.elementor-control-input-wrapper, .elementor-control-gapbetweenvideos .elementor-slider-input,  .elementor-control-columns .elementor-control-input-wrapper.elementor-control-unit-5 {
    width: 54px!important;
}
.embedpress-pro-control .elementor-control-input-wrapper {
    filter: grayscale(1);
}


.embedpress-pro-control:after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%!important;
    display: block;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 99;
    background: transparent!important;
    pointer-events: none;
}

.embedpress-pro-control.not-active {
    pointer-events: none;
}


/*EmbedPress Pro Controls Style Ends*/