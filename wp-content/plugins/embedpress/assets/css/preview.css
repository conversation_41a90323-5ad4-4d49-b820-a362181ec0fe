/**
 * @package     EmbedPress
 * <AUTHOR> <<EMAIL>>
 * @copyright   Copyright (C) 2018 EmbedPress. All rights reserved.
 * @license     GPLv2 or later
 * @since       1.0
 */

#tinymce {
    max-width: 100% !important;
}

.embedpress_wrapper {
    position: relative;
    padding: 1px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.embedpress_wrapper.is-loading {
    cursor: progress;
}

.embedpress_wrapper.dynamic-width {
    display: inline-block;
}

.embedpress_wrapper .loader-indicator {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background-color: rgba(255, 255, 255, .7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999999;
    pointer-events: none;
    opacity: 0;
    -webkit-transition: all 250ms ease-in;
    -moz-transition: all 250ms ease-in;
    -o-transition: all 250ms ease-in;
    transition: all 250ms ease-in;
}

.embedpress_wrapper .loader-indicator.is-loading {
    -webkit-transition: all 500ms ease-out;
    -moz-transition: all 500ms ease-out;
    -o-transition: all 500ms ease-out;
    transition: all 500ms ease-out;
    opacity: 1;
}

.embedpress_controller_panel {
    display: inline-block;
    position: absolute;
    width: initial;
    padding: 8px;
    margin-left: -45px;
    top: 1px;
    left: 50%;
    background: #796FAB;
    border: 1px solid rgba(151, 142, 196, 0.8);
    -webkit-box-shadow: 0px 10px 44px -5px rgba(0, 0, 0, 0.66);
    -moz-box-shadow: 0px 10px 44px -5px rgba(0, 0, 0, 0.66);
    box-shadow: 0px 10px 44px -5px rgba(0, 0, 0, 0.66);
    z-index: 999999;
    cursor: default;
}

.embedpress_controller_panel div.embedpress_controller_button {
    color: #fff;
    margin: 0 5px;
    font-size: 15px;
    cursor: pointer;
    display: inline-block;
    width: 20px;
}

.embedpress_controller_panel div.embedpress_controller_button:hover {
    text-decoration: none;
    color: #978EC4;
}

.embedpress_controller_panel.hidden {
    visibility: hidden;
}

.embedpress_wrapper.embedpress_placeholder {
    display: block;
    padding: 10px;
    background: #796FAB;
    border: 1px solid #978EC4;
    height: 120px;
    width: 450px;
    text-align: center;
    box-sizing: border-box;
    color: #FFF;
}

.embedpress_wrapper.embedpress_placeholder:before {
    content: attr(data-loading-text);
    font-weight: bold;
    font-family: 'Source Sans Pro', sans-serif;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    margin: auto;
}

.embedpress_wrapper img {
    max-width: 100%;
    width: auto \9;
    height: auto;
    vertical-align: middle;
    border: 0;
}

.modal {
    text-align: center;
}

@media screen and (min-width: 768px) {
    .modal:before {
        display: inline-block;
        vertical-align: middle;
        content: " ";
        height: 100%;
    }
}

.modal-dialog {
    display: inline-block;
    text-align: left;
    vertical-align: middle;
}

span.hidden {
    display: none;
}

.bootbox.modal {
    z-index: 100012;
}

.modal-dialog .row {
    width: 100%;
}

/* Fix issuu preview */
.ose-issuu.responsive {
    padding-bottom: 71.25% !important;
}
