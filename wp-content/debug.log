[10-Jul-2025 10:53:19 UTC] EmbedPress: enqueue_editor_assets called
[10-Jul-2025 10:53:19 UTC] EmbedPress: enqueue_single_asset called for embedpress-blocks at https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 10:53:19 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 10:53:19 UTC] EmbedPress: enqueue_single_asset called for embedpress-gutenberg at https://embedpress.test/wp-content/plugins/embedpress/assets/js/gutenberg.build.js
[10-Jul-2025 10:53:19 UTC] EmbedPress: enqueue_single_asset called for embedpress-blocks at https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 10:53:19 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 10:53:19 UTC] EmbedPress: enqueue_single_asset called for embedpress-gutenberg at https://embedpress.test/wp-content/plugins/embedpress/assets/js/gutenberg.build.js
[10-Jul-2025 10:53:19 UTC] EmbedPress: enqueue_single_asset called for embedpress-admin-common at https://embedpress.test/wp-content/plugins/embedpress/assets/js/admin-common.build.js
[10-Jul-2025 10:53:19 UTC] EmbedPress: enqueue_single_asset called for embedpress-admin at https://embedpress.test/wp-content/plugins/embedpress/assets/js/admin.build.js
[10-Jul-2025 10:53:19 UTC] EmbedPress: enqueue_single_asset called for embedpress-blocks at https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 10:53:19 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 10:53:19 UTC] EmbedPress: enqueue_single_asset called for embedpress-gutenberg at https://embedpress.test/wp-content/plugins/embedpress/assets/js/gutenberg.build.js
[10-Jul-2025 10:53:23 UTC] EmbedPress: enqueue_single_asset called for embedpress-common at https://embedpress.test/wp-content/plugins/embedpress/assets/js/common.build.js
[10-Jul-2025 10:53:23 UTC] EmbedPress: enqueue_single_asset called for embedpress-frontend at https://embedpress.test/wp-content/plugins/embedpress/assets/js/frontend.build.js
[10-Jul-2025 10:53:23 UTC] EmbedPress: enqueue_single_asset called for embedpress-video-player at https://embedpress.test/wp-content/plugins/embedpress/assets/js/video-player.build.js
[10-Jul-2025 10:53:23 UTC] EmbedPress: enqueue_single_asset called for embedpress-common at https://embedpress.test/wp-content/plugins/embedpress/assets/js/common.build.js
[10-Jul-2025 10:53:23 UTC] EmbedPress: enqueue_single_asset called for embedpress-carousel at https://embedpress.test/wp-content/plugins/embedpress/assets/js/carousel.build.js
[10-Jul-2025 10:53:23 UTC] EmbedPress: enqueue_single_asset called for embedpress-pdf-viewer at https://embedpress.test/wp-content/plugins/embedpress/assets/js/pdf-viewer.build.js
[10-Jul-2025 10:53:23 UTC] EmbedPress: enqueue_single_asset called for embedpress-frontend at https://embedpress.test/wp-content/plugins/embedpress/assets/js/frontend.build.js
[10-Jul-2025 10:53:23 UTC] EmbedPress: enqueue_single_asset called for embedpress-gallery at https://embedpress.test/wp-content/plugins/embedpress/assets/js/gallery.build.js
[10-Jul-2025 10:53:23 UTC] EmbedPress: enqueue_single_asset called for embedpress-video-player at https://embedpress.test/wp-content/plugins/embedpress/assets/js/video-player.build.js
[10-Jul-2025 10:53:23 UTC] EmbedPress: enqueue_single_asset called for embedpress-carousel at https://embedpress.test/wp-content/plugins/embedpress/assets/js/carousel.build.js
[10-Jul-2025 10:53:23 UTC] EmbedPress: enqueue_single_asset called for embedpress-document-viewer at https://embedpress.test/wp-content/plugins/embedpress/assets/js/document-viewer.build.js
[10-Jul-2025 10:53:23 UTC] EmbedPress: enqueue_single_asset called for embedpress-embed-ui at https://embedpress.test/wp-content/plugins/embedpress/assets/js/embed-ui.build.js
[10-Jul-2025 10:53:23 UTC] EmbedPress: enqueue_single_asset called for embedpress-pdf-viewer at https://embedpress.test/wp-content/plugins/embedpress/assets/js/pdf-viewer.build.js
[10-Jul-2025 10:53:23 UTC] EmbedPress: enqueue_single_asset called for embedpress-gallery at https://embedpress.test/wp-content/plugins/embedpress/assets/js/gallery.build.js
[10-Jul-2025 10:53:23 UTC] EmbedPress: enqueue_single_asset called for embedpress-document-viewer at https://embedpress.test/wp-content/plugins/embedpress/assets/js/document-viewer.build.js
[10-Jul-2025 10:53:23 UTC] EmbedPress: enqueue_single_asset called for embedpress-ads at https://embedpress.test/wp-content/plugins/embedpress/assets/js/ads.build.js
[10-Jul-2025 10:53:23 UTC] EmbedPress: enqueue_single_asset called for embedpress-embed-ui at https://embedpress.test/wp-content/plugins/embedpress/assets/js/embed-ui.build.js
[10-Jul-2025 10:53:23 UTC] EmbedPress: enqueue_single_asset called for embedpress-ads at https://embedpress.test/wp-content/plugins/embedpress/assets/js/ads.build.js
[10-Jul-2025 10:53:24 UTC] EmbedPress: enqueue_single_asset called for embedpress-common at https://embedpress.test/wp-content/plugins/embedpress/assets/js/common.build.js
[10-Jul-2025 10:53:24 UTC] EmbedPress: enqueue_single_asset called for embedpress-frontend at https://embedpress.test/wp-content/plugins/embedpress/assets/js/frontend.build.js
[10-Jul-2025 10:53:24 UTC] EmbedPress: enqueue_single_asset called for embedpress-video-player at https://embedpress.test/wp-content/plugins/embedpress/assets/js/video-player.build.js
[10-Jul-2025 10:53:24 UTC] EmbedPress: enqueue_single_asset called for embedpress-carousel at https://embedpress.test/wp-content/plugins/embedpress/assets/js/carousel.build.js
[10-Jul-2025 10:53:24 UTC] EmbedPress: enqueue_single_asset called for embedpress-pdf-viewer at https://embedpress.test/wp-content/plugins/embedpress/assets/js/pdf-viewer.build.js
[10-Jul-2025 10:53:24 UTC] EmbedPress: enqueue_single_asset called for embedpress-gallery at https://embedpress.test/wp-content/plugins/embedpress/assets/js/gallery.build.js
[10-Jul-2025 10:53:24 UTC] EmbedPress: enqueue_single_asset called for embedpress-document-viewer at https://embedpress.test/wp-content/plugins/embedpress/assets/js/document-viewer.build.js
[10-Jul-2025 10:53:24 UTC] EmbedPress: enqueue_single_asset called for embedpress-embed-ui at https://embedpress.test/wp-content/plugins/embedpress/assets/js/embed-ui.build.js
[10-Jul-2025 10:53:24 UTC] EmbedPress: enqueue_single_asset called for embedpress-ads at https://embedpress.test/wp-content/plugins/embedpress/assets/js/ads.build.js
[10-Jul-2025 10:53:24 UTC] EmbedPress: enqueue_single_asset called for embedpress-common at https://embedpress.test/wp-content/plugins/embedpress/assets/js/common.build.js
[10-Jul-2025 10:53:24 UTC] EmbedPress: enqueue_single_asset called for embedpress-frontend at https://embedpress.test/wp-content/plugins/embedpress/assets/js/frontend.build.js
[10-Jul-2025 10:53:24 UTC] EmbedPress: enqueue_single_asset called for embedpress-video-player at https://embedpress.test/wp-content/plugins/embedpress/assets/js/video-player.build.js
[10-Jul-2025 10:53:24 UTC] EmbedPress: enqueue_single_asset called for embedpress-carousel at https://embedpress.test/wp-content/plugins/embedpress/assets/js/carousel.build.js
[10-Jul-2025 10:53:24 UTC] EmbedPress: enqueue_single_asset called for embedpress-pdf-viewer at https://embedpress.test/wp-content/plugins/embedpress/assets/js/pdf-viewer.build.js
[10-Jul-2025 10:53:24 UTC] EmbedPress: enqueue_single_asset called for embedpress-gallery at https://embedpress.test/wp-content/plugins/embedpress/assets/js/gallery.build.js
[10-Jul-2025 10:53:24 UTC] EmbedPress: enqueue_single_asset called for embedpress-document-viewer at https://embedpress.test/wp-content/plugins/embedpress/assets/js/document-viewer.build.js
[10-Jul-2025 10:53:24 UTC] EmbedPress: enqueue_single_asset called for embedpress-embed-ui at https://embedpress.test/wp-content/plugins/embedpress/assets/js/embed-ui.build.js
[10-Jul-2025 10:53:24 UTC] EmbedPress: enqueue_single_asset called for embedpress-ads at https://embedpress.test/wp-content/plugins/embedpress/assets/js/ads.build.js
[10-Jul-2025 10:53:35 UTC] EmbedPress: enqueue_editor_assets called
[10-Jul-2025 10:53:35 UTC] EmbedPress: enqueue_single_asset called for embedpress-blocks at https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 10:53:35 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 10:53:35 UTC] EmbedPress: enqueue_single_asset called for embedpress-gutenberg at https://embedpress.test/wp-content/plugins/embedpress/assets/js/gutenberg.build.js
[10-Jul-2025 10:53:35 UTC] EmbedPress: enqueue_single_asset called for embedpress-blocks at https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 10:53:35 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 10:53:35 UTC] EmbedPress: enqueue_single_asset called for embedpress-gutenberg at https://embedpress.test/wp-content/plugins/embedpress/assets/js/gutenberg.build.js
[10-Jul-2025 10:53:35 UTC] EmbedPress: enqueue_single_asset called for embedpress-admin-common at https://embedpress.test/wp-content/plugins/embedpress/assets/js/admin-common.build.js
[10-Jul-2025 10:53:35 UTC] EmbedPress: enqueue_single_asset called for embedpress-admin at https://embedpress.test/wp-content/plugins/embedpress/assets/js/admin.build.js
[10-Jul-2025 10:53:35 UTC] EmbedPress: enqueue_single_asset called for embedpress-blocks at https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 10:53:35 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 10:53:35 UTC] EmbedPress: enqueue_single_asset called for embedpress-gutenberg at https://embedpress.test/wp-content/plugins/embedpress/assets/js/gutenberg.build.js
[10-Jul-2025 10:55:23 UTC] EmbedPress: enqueue_editor_assets called
[10-Jul-2025 10:55:23 UTC] EmbedPress: enqueue_single_asset called for embedpress-blocks at https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 10:55:23 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 10:55:23 UTC] EmbedPress: enqueue_single_asset called for embedpress-gutenberg at https://embedpress.test/wp-content/plugins/embedpress/assets/js/gutenberg.build.js
[10-Jul-2025 10:55:23 UTC] EmbedPress: enqueue_single_asset called for embedpress-blocks at https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 10:55:23 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 10:55:23 UTC] EmbedPress: enqueue_single_asset called for embedpress-gutenberg at https://embedpress.test/wp-content/plugins/embedpress/assets/js/gutenberg.build.js
[10-Jul-2025 10:55:23 UTC] EmbedPress: enqueue_single_asset called for embedpress-admin-common at https://embedpress.test/wp-content/plugins/embedpress/assets/js/admin-common.build.js
[10-Jul-2025 10:55:23 UTC] EmbedPress: enqueue_single_asset called for embedpress-admin at https://embedpress.test/wp-content/plugins/embedpress/assets/js/admin.build.js
[10-Jul-2025 10:55:23 UTC] EmbedPress: enqueue_single_asset called for embedpress-blocks at https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 10:55:23 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 10:55:23 UTC] EmbedPress: enqueue_single_asset called for embedpress-gutenberg at https://embedpress.test/wp-content/plugins/embedpress/assets/js/gutenberg.build.js
[10-Jul-2025 11:12:13 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 11:12:13 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 11:12:13 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 11:12:16 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 11:12:16 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 11:12:17 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 11:12:17 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 11:15:20 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 11:15:20 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 11:15:20 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 11:15:25 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 11:15:25 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 11:15:25 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 11:15:25 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[10-Jul-2025 13:39:06 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[13-Jul-2025 02:00:35 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[13-Jul-2025 02:00:36 UTC] Automatic updates starting...
[13-Jul-2025 02:00:36 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[13-Jul-2025 02:00:37 UTC] EmbedPress: Successfully enqueued blocks script: https://embedpress.test/wp-content/plugins/embedpress/assets/js/blocks.build.js
[13-Jul-2025 02:00:39 UTC]   Automatic plugin updates starting...
[13-Jul-2025 02:00:39 UTC]   Automatic plugin updates complete.
[13-Jul-2025 02:00:40 UTC]   Automatic theme updates starting...
[13-Jul-2025 02:00:40 UTC]   Automatic theme updates complete.
[13-Jul-2025 02:00:40 UTC] Automatic updates complete.
[13-Jul-2025 02:00:43 UTC] WordPress database error Unknown column 'DATE(created_at)' in 'field list' for query SELECT DISTINCT `DATE(created_at)` as date_start,
					DATE_ADD(`DATE(created_at)`, INTERVAL 1 DAY) as date_end,
					page_id
				 FROM `wp_greenmetrics_stats`
				 WHERE created_at < '2025-06-13 00:00:00'
				 GROUP BY `page_id, DATE(created_at)` made by require('wp-cron.php'), do_action_ref_array('greenmetrics_data_management'), WP_Hook->do_action, WP_Hook->apply_filters, GreenMetrics\GreenMetrics_Data_Manager::run_scheduled_data_management, GreenMetrics\GreenMetrics_Data_Manager->aggregate_old_data
